worker_processes auto;
pid /run/nginx.pid;

events {
  worker_connections  4096;
  multi_accept on;
  use epoll;
}
 
http {
  server_tokens off;
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  keepalive_timeout 65;
  types_hash_max_size 2048;
  include /etc/nginx/mime.types;
  default_type application/octet-stream;
  access_log off;  # Desabilitar para performance
  error_log /var/log/nginx/error.log warn;
  real_ip_header X-Real-IP;
  charset utf-8;

  # Gzip otimizado
  gzip on;
  gzip_vary on;
  gzip_min_length 1024;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

  # Cache de arquivos
  open_file_cache max=1000 inactive=20s;
  open_file_cache_valid 30s;
  open_file_cache_min_uses 2;
  open_file_cache_errors on;

  include /etc/nginx/conf.d/*.conf;
  include /etc/nginx/sites-available/*;
}