<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\OrdersApi;

class DashboardController extends Controller
{
    /**
     * Verifica se o modo debug está ativo
     */
    private function isDebugMode()
    {
        return session('dashboard_debug', false);
    }

    /**
     * Exibe o dashboard principal
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Carregar dados das ordens para exibir no dashboard
        $ordersApi = new OrdersApi();
        $ordersData = $ordersApi->processOrders($this->isDebugMode());

        // Buscar últimos 100 registros da tabela tbl_orders
        $dbOrders = $this->getDbOrders();

        // Buscar últimos 20 registros da tabela order_status_timeline
        $statusTimeline = $this->getStatusTimeline();

        // Calcular estatísticas de vendas
        $salesStats = $this->calculateSalesStatistics();

        return view('dashboard.index', compact('ordersData', 'dbOrders', 'statusTimeline', 'salesStats'));
    }

    /**
     * Busca novas ordens via AJAX
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrders()
    {
        // Cache das ordens API por 30 segundos
        $ordersData = Cache::remember('dashboard_orders_api', 30, function () {
            $ordersApi = new OrdersApi();
            return $ordersApi->processOrders($this->isDebugMode());
        });

        // Cache dos dados do banco por 60 segundos
        $dbOrders = Cache::remember('dashboard_db_orders', 60, function () {
            return $this->getDbOrders();
        });

        // Cache do status timeline por 30 segundos
        $statusTimeline = Cache::remember('dashboard_status_timeline', 30, function () {
            return $this->getStatusTimeline();
        });

        // Cache das estatísticas de vendas por 5 minutos
        $salesStats = Cache::remember('dashboard_sales_stats', 300, function () {
            return $this->calculateSalesStatistics();
        });

        // Cache dos marketplaces por 10 minutos
        $marketplaces = Cache::remember('dashboard_marketplaces', 600, function () {
            $ordersApi = new OrdersApi();
            return $ordersApi->curl_get_marketplaces();
        });

        return response()->json([
            'success' => true,
            'data' => $ordersData,
            'db_orders' => $dbOrders,
            'status_timeline' => $statusTimeline,
            'sales_stats' => $salesStats,
            'marketplaces' => $marketplaces
        ]);
    }

    /**
     * Método para futuras interações com formulários
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processForm(Request $request)
    {
        $acao = $request->input('acao');
        $parametro1 = $request->input('parametro1');
        $parametro2 = $request->input('parametro2');
        $observacoes = $request->input('observacoes');

        // Processar diferentes ações
        switch ($acao) {
            case 'criar_ordem':
                return $this->criarOrdem($parametro1, $parametro2, $observacoes);

            case 'atualizar_status':
                return $this->atualizarStatus($parametro1, $parametro2, $observacoes);

            case 'gerar_relatorio':
                return $this->gerarRelatorio($parametro1, $parametro2, $observacoes);

            case 'configurar_sistema':
                return $this->configurarSistema($parametro1, $parametro2, $observacoes);

            default:
                return response()->json([
                    'success' => false,
                    'message' => 'Ação não reconhecida'
                ]);
        }
    }

    /**
     * Criar nova ordem
     */
    private function criarOrdem($param1, $param2, $obs)
    {
        // Implementar lógica para criar ordem
        return response()->json([
            'success' => true,
            'message' => "Nova ordem criada com parâmetros: {$param1}, {$param2}"
        ]);
    }

    /**
     * Atualizar status
     */
    private function atualizarStatus($param1, $param2, $obs)
    {
        // Implementar lógica para atualizar status
        return response()->json([
            'success' => true,
            'message' => "Status atualizado para: {$param1}"
        ]);
    }

    /**
     * Gerar relatório
     */
    private function gerarRelatorio($param1, $param2, $obs)
    {
        // Implementar lógica para gerar relatório
        return response()->json([
            'success' => true,
            'message' => "Relatório gerado com filtros: {$param1}, {$param2}"
        ]);
    }

    /**
     * Configurar sistema
     */
    private function configurarSistema($param1, $param2, $obs)
    {
        // Implementar lógica para configurar sistema
        return response()->json([
            'success' => true,
            'message' => "Sistema configurado: {$param1} = {$param2}"
        ]);
    }

    /**
     * Busca últimos 100 registros da tabela tbl_orders
     *
     * @return array
     */
    private function getDbOrders()
    {
        try {
            $orders = DB::table('tbl_orders')
                ->orderBy('created_at', 'desc')
                ->limit(100)
                ->get();

            return $orders->map(function ($order) {
                return [
                    'id' => $order->id ?? 'N/A',
                    'user_id' => $order->user_id ?? 'N/A',
                    'order_id' => $order->reference ?? 'N/A', // reference é o order_id
                    'country' => $order->country ?? 'N/A',
                    'status' => $order->state ?? 'N/A', // state é o status
                    'channel' => $order->channel ?? 'N/A',
                    'price' => $order->total_price ?? 0, // total_price é o price
                    'commission' => $order->total_commission ?? 0, // nova coluna comissão
                    'product_quantity' => $order->product_quantity ?? 1,
                    'created_at' => $order->created_at ?? now(),
                    'updated_at' => $order->updated_at ?? now()
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados da tabela tbl_orders: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Busca últimos 20 registros da tabela order_status_timeline
     *
     * @param string|null $orderId
     * @return array
     */
    private function getStatusTimeline($orderId = null)
    {
        try {
            Log::info('getStatusTimeline chamado', ['order_id' => $orderId]);

            // Primeiro, vamos verificar se a tabela existe e tem dados
            $tableExists = DB::select("SHOW TABLES LIKE 'order_status_timeline'");
            Log::info('Tabela existe?', ['exists' => !empty($tableExists)]);

            if (empty($tableExists)) {
                Log::error('Tabela order_status_timeline não existe');
                return [];
            }

            // Verificar total de registros na tabela
            $totalCount = DB::table('order_status_timeline')->count();
            Log::info('Total de registros na tabela', ['total' => $totalCount]);

            $query = DB::table('order_status_timeline');

            if ($orderId) {
                Log::info('Buscando por order_id específico', ['order_id' => $orderId]);
                // Se order_id fornecido, buscar todos os registros dessa ordem (ASC)
                $timeline = $query->where('order_id', $orderId)
                    ->orderBy('created_at', 'asc')
                    ->get();
            } else {
                Log::info('Buscando últimos 20 registros');
                // Caso contrário, buscar últimos 20 registros (DESC)
                $timeline = $query->orderBy('created_at', 'desc')
                    ->limit(20)
                    ->get();
            }

            Log::info('Registros encontrados na query', ['count' => $timeline->count()]);

            if ($timeline->count() > 0) {
                Log::info('Primeiro registro encontrado', ['first_record' => $timeline->first()]);
            }

            $result = $timeline->map(function ($item) {
                return [
                    'order_id' => $item->order_id ?? 'N/A',
                    'status' => $item->status ?? 'N/A',
                    'channel' => $item->channel ?? 'N/A',
                    'country' => $item->country ?? 'N/A',
                    'data' => $item->data ?? 'N/A',
                    'hora' => $item->hora ?? 'N/A',
                    'created_at' => $item->created_at ?? now()
                ];
            })->toArray();

            Log::info('Resultado final', ['count' => count($result)]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Erro ao buscar dados da tabela order_status_timeline: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }



    /**
     * Buscar ordem por reference na tabela tbl_orders - VERSÃO SIMPLIFICADA
     */
    public function searchOrder(Request $request)
    {
        Log::info('🔍 BUSCA SIMPLES INICIADA', $request->all());

        try {
            $orderNumber = $request->input('order_number');

            if (empty($orderNumber)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Digite um número de ordem'
                ]);
            }

            Log::info('📝 Buscando ordem:', ['reference' => $orderNumber]);

            // USAR EXATAMENTE A MESMA QUERY DA TABELA DATABASE ORDERS
            $order = DB::table('tbl_orders')
                ->where('reference', $orderNumber)
                ->first();

            if ($order) {
                Log::info('✅ ORDEM ENCONTRADA!', ['id' => $order->id]);

                return response()->json([
                    'success' => true,
                    'message' => 'Ordem encontrada com sucesso',
                    'data' => (array) $order
                ]);
            } else {
                Log::warning('❌ Ordem não encontrada');

                // Debug: mostrar alguns exemplos
                $samples = DB::table('tbl_orders')
                    ->select('reference')
                    ->limit(3)
                    ->get()
                    ->pluck('reference')
                    ->toArray();

                Log::info('📋 Exemplos na tabela:', $samples);

                return response()->json([
                    'success' => false,
                    'message' => 'Ordem não encontrada. Exemplos: ' . implode(', ', $samples)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('❌ ERRO NA BUSCA:', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erro: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Calcular estatísticas de vendas mensais e diárias
     */
    private function calculateSalesStatistics()
    {
        try {
            // Datas para cálculos
            $primeiroDia = date('Y-m-01'); // Primeiro dia do mês atual
            $ultimoDia = date('Y-m-t');    // Último dia do mês atual
            $dataHoje = date('Y-m-d');     // Data de hoje

            Log::info('Calculando estatísticas de vendas', [
                'primeiro_dia' => $primeiroDia,
                'ultimo_dia' => $ultimoDia,
                'data_hoje' => $dataHoje
            ]);

            // Buscar estatísticas do mês atual com agregação SQL (OTIMIZADO)
            $estatisticasMes = DB::table('tbl_orders')
                ->selectRaw('
                    COUNT(*) as quantidade,
                    COALESCE(SUM(total_price), 0) as total_vendas,
                    COALESCE(SUM(total_commission), 0) as total_comissao
                ')
                ->whereRaw('DATE(created_at) >= ?', [$primeiroDia])
                ->whereRaw('DATE(created_at) <= ?', [$ultimoDia])
                ->first();

            // Usar resultados da agregação SQL
            $quantidadeVendasMes = $estatisticasMes->quantidade ?? 0;
            $totalVendasMes = floatval($estatisticasMes->total_vendas ?? 0);
            $totalComissaoMes = floatval($estatisticasMes->total_comissao ?? 0);

            // Buscar estatísticas do dia atual com agregação SQL (OTIMIZADO)
            $estatisticasDia = DB::table('tbl_orders')
                ->selectRaw('
                    COUNT(*) as quantidade,
                    COALESCE(SUM(total_price), 0) as total_vendas,
                    COALESCE(SUM(total_commission), 0) as total_comissao
                ')
                ->whereRaw('DATE(created_at) = ?', [$dataHoje])
                ->first();

            // Usar resultados da agregação SQL
            $quantidadeVendasDia = $estatisticasDia->quantidade ?? 0;
            $totalVendasDia = floatval($estatisticasDia->total_vendas ?? 0);
            $totalComissaoDia = floatval($estatisticasDia->total_comissao ?? 0);

            // Calcular valores líquidos (total - comissão)
            $totalLiquidoMes = $totalVendasMes - $totalComissaoMes;
            $totalLiquidoDia = $totalVendasDia - $totalComissaoDia;

            $estatisticas = [
                'mes' => [
                    'quantidade' => $quantidadeVendasMes,
                    'total_vendas' => $totalVendasMes,
                    'total_comissao' => $totalComissaoMes,
                    'total_liquido' => $totalLiquidoMes,
                    'periodo' => $primeiroDia . ' a ' . $ultimoDia
                ],
                'dia' => [
                    'quantidade' => $quantidadeVendasDia,
                    'total_vendas' => $totalVendasDia,
                    'total_comissao' => $totalComissaoDia,
                    'total_liquido' => $totalLiquidoDia,
                    'data' => $dataHoje
                ]
            ];

            Log::info('Estatísticas calculadas', $estatisticas);

            return $estatisticas;

        } catch (\Exception $e) {
            Log::error('Erro ao calcular estatísticas de vendas: ' . $e->getMessage());
            return [
                'mes' => ['quantidade' => 0, 'total_vendas' => 0, 'total_comissao' => 0, 'total_liquido' => 0, 'periodo' => 'N/A'],
                'dia' => ['quantidade' => 0, 'total_vendas' => 0, 'total_comissao' => 0, 'total_liquido' => 0, 'data' => 'N/A']
            ];
        }
    }

    /**
     * Buscar timeline por order_id
     */
    public function searchTimeline(Request $request)
    {
        Log::info('🔍 BUSCA TIMELINE INICIADA', $request->all());

        try {
            $orderNumber = $request->input('order_number');

            if (empty($orderNumber)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Digite um número de ordem'
                ]);
            }

            Log::info('📝 Buscando timeline para ordem:', ['order_id' => $orderNumber]);

            // Buscar todos os registros do timeline para esta ordem
            $timeline = $this->getStatusTimeline($orderNumber);

            if (!empty($timeline)) {
                Log::info('✅ TIMELINE ENCONTRADO!', ['count' => count($timeline)]);

                return response()->json([
                    'success' => true,
                    'message' => 'Timeline encontrado com sucesso',
                    'data' => $timeline
                ]);
            } else {
                Log::warning('❌ Timeline não encontrado');

                // Debug: mostrar alguns exemplos
                $samples = DB::table('order_status_timeline')
                    ->select('order_id')
                    ->distinct()
                    ->limit(3)
                    ->get()
                    ->pluck('order_id')
                    ->toArray();

                Log::info('📋 Exemplos na tabela timeline:', $samples);

                return response()->json([
                    'success' => false,
                    'message' => 'Timeline não encontrado. Exemplos: ' . implode(', ', $samples)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('❌ ERRO NA BUSCA TIMELINE:', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erro: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Busca detalhes de uma order específica via API
     */
    public function getOrderDetails($orderId)
    {
        try {
            Log::info("Buscando detalhes da order via API: {$orderId}");

            $orderDetails = OrdersApi::getOrderDetails($orderId);

            if ($orderDetails) {
                return response()->json([
                    'success' => true,
                    'data' => $orderDetails
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Order não encontrada ou erro na API'
                ], 404);
            }

        } catch (\Exception $e) {
            Log::error('Erro ao buscar detalhes da order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor'
            ], 500);
        }
    }

    /**
     * Alternar modo debug
     */
    public function toggleDebug()
    {
        $currentDebug = $this->isDebugMode();
        $newDebug = !$currentDebug;

        session(['dashboard_debug' => $newDebug]);

        return response()->json([
            'success' => true,
            'debug_mode' => $newDebug,
            'message' => 'Modo debug ' . ($newDebug ? 'ativado' : 'desativado')
        ]);
    }
}
