@php
function getStatusClass($status) {
    switch(strtolower($status)) {
        case 'waiting_acceptance':
        case 'waitingacceptance':
            return 'status-waiting';
        case 'waiting_debit_payment':
        case 'waiting_debit':
            return 'status-payment';
        case 'staging':
            return 'status-staging';
        case 'created':
            return 'status-created';
        default:
            return 'status-default';
    }
}

function translateStatus($status) {
    switch(strtolower($status)) {
        case 'waiting_acceptance':
        case 'waitingacceptance':
            return 'WAITING ACCEPT';
        case 'waiting_debit_payment':
        case 'waiting_debit':
            return 'WAITING PAYMENT';
        case 'staging':
            return 'STAGING';
        case 'created':
            return 'CREATED';
        default:
            return strtoupper($status);
    }
}
@endphp

<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #212529;
            color: white;
            font-size: 0.8rem;
        }

        .content {
            padding: 15px;
        }

        .card {
            background-color: #2c3034;
            border: none;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .card-body {
            padding: 15px;
        }

        .card-title {
            font-size: 0.8rem;
        }

        .statistic-value {
            font-size: 20px;
            font-weight: bold;
        }

        .card .statistic-value {
            font-size: 20px !important;
        }

        .card .d-flex .statistic-value {
            font-size: 16px !important;
        }

        .trend-up {
            color: #28a745;
        }

        .trend-down {
            color: #dc3545;
        }

        .table {
            color: white;
            font-size: 0.75rem;
        }

        .positive-value {
            color: #1db954;
        }

        .negative-value {
            color: #f45b5b;
        }

        .valor-zero {
            color: #ffc107;
        }

        .robo-info {
            color: #626262;
            font-size: 0.65em;
            display: block;
        }

        .data-info {
            color: #626262;
            font-size: 0.65em;
            display: block;
        }

        .statistic-label {
            font-size: 0.8rem;
            color: #adb5bd;
            margin-right: 5px;
        }

        .martingale-label {
            font-size: 0.8rem;
            color: #adb5bd;
            font-weight: bold;
            margin-bottom: 5px;
        }

        /* Estilos para os relógios digitais */
        .clocks-container {
            position: absolute;
            top: 15px;
            right: 20px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            max-width: 500px;
            justify-content: flex-end;
        }

        .digital-clock {
            display: flex;
            align-items: center;
            font-size: 1.1rem;
            color: #fff;
            margin-bottom: 5px;

        }

        .digital-clock img {
            width: 20px;
            height: 14px;
            margin-right: 5px;
        }

        /* Estilos para formulário */
        .form-control {
            background-color: #2c3034;
            border: 1px solid #454d55;
            color: white;
        }

        .form-control:focus {
            background-color: #2c3034;
            border-color: #007bff;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }

        /* Estilos para tabelas vazias */
        .empty-table {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        /* Estilos para status das ordens */
        .status-waiting {
            background-color: #ffc107;
            color: #000;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-staging {
            background-color: #007bff;
            color: #fff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-created {
            background-color: #28a745;
            color: #fff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-payment {
            background-color: #17a2b8;
            color: #fff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .status-default {
            background-color: #6c757d;
            color: #fff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        /* Estilos para as tabelas de ordens */
        .orders-table {
            font-size: 0.8rem;
        }

        .orders-table th {
            background-color: #343a40;
            color: #fff;
            font-weight: bold;
            text-align: left;
            padding: 4px 6px;
            font-size: 0.75rem;
        }

        .orders-table td {
            padding: 3px 6px;
            text-align: left;
            vertical-align: middle;
            font-size: 0.75rem;
        }

        .table-container {
            max-height: 350px;
            overflow-y: auto;
        }

        /* Animação para novas ordens */
        .new-order {
            animation: highlightNew 2s ease-in-out;
        }

        @keyframes highlightNew {
            0% { background-color: #28a745; }
            100% { background-color: transparent; }
        }

        /* Estilo para o countdown timer */
        #countdown-timer {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 1rem;
            padding: 8px 12px;
            border-radius: 6px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Estilos para notificações */
        .notification {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 350px;
            font-size: 14px;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-content i {
            font-size: 16px;
            animation: bell-ring 1s ease-in-out;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-left: auto;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-close:hover {
            opacity: 0.7;
        }

        @keyframes bell-ring {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(10deg); }
            75% { transform: rotate(-10deg); }
        }

        /* Barra de alerta IT Team */
        .it-team-alert {
            background: linear-gradient(90deg, #dc3545, #c82333);
            color: white;
            padding: 12px 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            animation: pulse-alert 2s infinite;
            border-left: 5px solid #a71e2a;
        }

        .it-team-alert i {
            margin-right: 8px;
            font-size: 1.1em;
        }

        /* Barra de alerta Transfer Orders */
        .transfer-alert {
            background: linear-gradient(90deg, #dc3545, #c82333);
            color: white;
            padding: 12px 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            animation: pulse-alert 2s infinite;
            border-left: 5px solid #a71e2a;
        }

        .transfer-alert i {
            margin-right: 8px;
            font-size: 1.1em;
        }

        @keyframes pulse-alert {
            0% { transform: scale(1); box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3); }
            50% { transform: scale(1.02); box-shadow: 0 6px 16px rgba(220, 53, 69, 0.5); }
            100% { transform: scale(1); box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3); }
        }




    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 content">
                <h4 class="mb-3">Dashboard</h4>

                <!-- Relógios digitais apenas para Portugal, Espanha, França e Alemanha -->
                <div class="clocks-container">
                    <!-- Relógio Portugal -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/pt.svg" alt="Bandeira de Portugal">
                        <span id="portugal-time">00:00:00</span>
                    </div>

                    <!-- Relógio Espanha -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/es.svg" alt="Bandeira da Espanha">
                        <span id="spain-time">00:00:00</span>
                    </div>

                    <!-- Relógio França -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/fr.svg" alt="Bandeira da França">
                        <span id="france-time">00:00:00</span>
                    </div>

                    <!-- Relógio Alemanha -->
                    <div class="digital-clock">
                        <img src="https://flagcdn.com/de.svg" alt="Bandeira da Alemanha">
                        <span id="germany-time">00:00:00</span>
                    </div>


                </div>

                <!-- Estatísticas das Ordens -->
                <div class="row">
                    <div class="col-md-1"></div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title mb-1" style="text-align: left;">
                                    Vendas
                                </h6>
                                <div class="mb-1">
                                    <div class="statistic-value" style="color: #28a745; font-size: 1.2em; text-align: left;" id="sales-month-display">
                                        <span style="color: #6c757d; font-size: 0.8em;">€</span>
                                        {{ isset($salesStats) ? number_format($salesStats['mes']['total_liquido'], 2) : '0.00' }}
                                        <span style="color: #6c757d; font-size: 0.8em;">({{ isset($salesStats) ? $salesStats['mes']['quantidade'] : 0 }})</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="statistic-value" style="color: #28a745; font-size: 1.2em; text-align: left;" id="sales-day-display">
                                        <span style="color: #6c757d; font-size: 0.8em;">€</span>
                                        {{ isset($salesStats) ? number_format($salesStats['dia']['total_liquido'], 2) : '0.00' }}
                                        <span style="color: #6c757d; font-size: 0.8em;">({{ isset($salesStats) ? $salesStats['dia']['quantidade'] : 0 }})</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    Estatísticas das Ordens
                                    <div class="float-end">
                                        <span class="badge bg-success me-2" id="countdown-timer">⏱️ 30s</span>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggle-debug">
                                            <i class="fas fa-bug"></i> <span id="debug-status">{{ isset($ordersData) && $ordersData['debug_mode'] ? 'ON' : 'OFF' }}</span>
                                        </button>
                                    </div>
                                </h6>

                                <!-- Área de resultado -->
                                <div id="form-result" class="mb-2" style="display: none;">
                                    <div class="alert alert-info alert-sm" role="alert">
                                        <span id="result-message">Resultado</span>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-1">
                                        <div class="martingale-label">Total</div>
                                        <div class="statistic-value" id="total-ativos">{{ isset($ordersData) ? ($ordersData['total_33'] + $ordersData['total_25']) : 0 }}</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="martingale-label">Transfer</div>
                                        <div class="statistic-value" style="color: #007bff;" id="greens-count">{{ isset($ordersData) ? $ordersData['total_33'] : 0 }}</div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="martingale-label">Support</div>
                                        <div class="statistic-value" style="color: #ffc107;" id="reds-count">{{ isset($ordersData) ? $ordersData['total_25'] : 0 }}</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="martingale-label">Waiting Accept</div>
                                        <div class="statistic-value" style="color: #ffc107;" id="last-hour-greens">{{ isset($ordersData) && isset($ordersData['waiting_acceptance']) ? count($ordersData['waiting_acceptance']) : 0 }}</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="martingale-label">Waiting Payment</div>
                                        <div class="statistic-value" style="color: #17a2b8;" id="last-hour-reds">{{ isset($ordersData) && isset($ordersData['waiting_debit_payment']) ? count($ordersData['waiting_debit_payment']) : 0 }}</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="martingale-label">Staging</div>
                                        <div class="statistic-value" style="color: #007bff;" id="last-hour-m2-positive">{{ isset($ordersData) && isset($ordersData['staging']) ? count($ordersData['staging']) : 0 }}</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="martingale-label">Created</div>
                                        <div class="statistic-value" style="color: #28a745;" id="last-hour-m2-negative">{{ isset($ordersData) && isset($ordersData['created']) ? count($ordersData['created']) : 0 }}</div>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>


                <!-- Área de Busca de Ordem -->
                <div class="row mt-3">
                    <div class="col-md-2."></div>
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title mb-1">
                                    <i class="fas fa-clock"></i> Buscar Timeline
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="timeline-search-input" placeholder="Digite o Order ID..." maxlength="20">
                                            <button class="btn btn-info" type="button" id="search-timeline-btn">
                                                <i class="fas fa-search"></i> Buscar
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="timeline-search-result" style="display: none;">
                                            <button class="btn btn-success" id="show-timeline-details-btn" style="display: none;">
                                                <i class="fas fa-eye"></i> Ver Detalhes
                                            </button>
                                            <span id="timeline-search-message" class="ms-2"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title mb-1">
                                    <i class="fas fa-search"></i> Buscar Ordem
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="order-search-input" placeholder="Digite o número da ordem..." maxlength="20">
                                            <button class="btn btn-primary" type="button" id="search-order-btn">
                                                <i class="fas fa-search"></i> Buscar
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="search-result" style="display: none;">
                                            <button class="btn btn-success" id="show-details-btn" style="display: none;">
                                                <i class="fas fa-eye"></i> Ver Detalhes
                                            </button>
                                            <span id="search-message" class="ms-2"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Primeira Linha de Tabelas -->
                <div class="row mt-3">
                    <!-- Tabela Ordens ID 25 (Suporte) -->
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    Support Orders (ID 25)
                                    <span class="badge bg-warning text-dark" id="count-25" style="font-size: 0.8rem;">
                                        {{ isset($ordersData) ? $ordersData['total_25'] : 0 }}
                                    </span>
                                </h5>
                                <div class="table-container">
                                    <table class="table table-dark table-hover orders-table">
                                        <thead>
                                            <tr>
                                                <th>Created</th>
                                                <th>User</th>
                                                <th>Order ID</th>
                                                <th>Channel</th>
                                                <th>Qty</th>
                                                <th>Price</th>
                                                <th>Status</th>
                                                <th>JSON</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orders-25-tbody">
                                            @if(isset($ordersData) && !empty($ordersData['orders_25']))
                                                @foreach($ordersData['orders_25'] as $order)
                                                    <tr>
                                                        <td>{{ \Carbon\Carbon::parse($order['order_created_at'])->format('d/m H:i') }}</td>
                                                        <td>{{ $order['user_id'] }}</td>
                                                        <td>{{ $order['order_id'] }}</td>
                                                        <td>{{ $order['channel'] }}</td>
                                                        <td>{{ $order['product_quantity'] }}</td>
                                                        <td>€{{ number_format($order['price'], 2) }}</td>
                                                        <td>
                                                            <span class="{{ getStatusClass($order['status']) }}">
                                                                {{ $order['status'] }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('{{ $order['order_id'] }}')">
                                                                <i class="fas fa-code"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabela Base de Dados -->
                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    Database Orders (100)
                                    <span class="badge bg-info" id="count-db" style="font-size: 0.8rem;">
                                        {{ isset($dbOrders) ? count($dbOrders) : 0 }}
                                    </span>
                                </h5>
                                <div class="table-container">
                                    <table class="table table-dark table-hover orders-table">
                                        <thead>
                                            <tr>
                                                <th>Created</th>
                                                <th>User</th>
                                                <th>Order ID</th>
                                                <th>Channel/Country</th>
                                                <th>Qty</th>
                                                <th>Price</th>
                                                <th>Commission</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orders-db-tbody">
                                            @if(isset($dbOrders) && !empty($dbOrders))
                                                @foreach($dbOrders as $order)
                                                    <tr>
                                                        <td>{{ \Carbon\Carbon::parse($order['created_at'])->format('d/m H:i') }}</td>
                                                        <td>{{ $order['user_id'] }}</td>
                                                        <td>{{ $order['order_id'] }}</td>
                                                        <td>{{ $order['channel'] }}-{{ $order['country'] }}</td>
                                                        <td>{{ $order['product_quantity'] }}</td>
                                                        <td>€{{ number_format($order['price'], 2) }}</td>
                                                        <td>€{{ number_format($order['commission'], 2) }}</td>
                                                        <td>
                                                            <span class="{{ getStatusClass($order['status']) }}">
                                                                {{ $order['status'] }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="empty-table">Nenhuma ordem encontrada na base de dados</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Segunda Linha de Tabelas -->
                <div class="row mt-3">

                <!-- Tabela Status Timeline -->
                <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    Status Timeline
                                    <span class="badge bg-success" id="count-timeline" style="font-size: 0.8rem;">
                                        {{ isset($statusTimeline) ? count($statusTimeline) : 0 }}
                                    </span>
                                </h5>
                                <div class="table-container" style="max-height: 350px; overflow-y: auto;">
                                    <table class="table table-dark table-hover table-sm">
                                        <thead class="sticky-top">
                                            <tr>
                                                <th>Order ID</th>
                                                <th>Status</th>
                                                <th>Info.</th>
                                            </tr>
                                        </thead>
                                        <tbody id="status-timeline-tbody">
                                            @if(isset($statusTimeline) && !empty($statusTimeline))
                                                @foreach($statusTimeline as $item)
                                                    @php
                                                        // Formatar data/hora para "08.06 - 13:10"
                                                        $data = $item['data'] ?? '';
                                                        $hora = $item['hora'] ?? '';

                                                        // Se data está no formato "2025-06-08"
                                                        if (strpos($data, '-') !== false) {
                                                            $dateParts = explode('-', $data);
                                                            if (count($dateParts) >= 3) {
                                                                $data = $dateParts[2] . '.' . $dateParts[1];
                                                            }
                                                        }

                                                        // Se hora está no formato "13:10:13"
                                                        if (strpos($hora, ':') !== false) {
                                                            $timeParts = explode(':', $hora);
                                                            if (count($timeParts) >= 2) {
                                                                $hora = $timeParts[0] . ':' . $timeParts[1];
                                                            }
                                                        }

                                                        $formattedDateTime = $data . ' - ' . $hora;
                                                    @endphp
                                                    <tr>
                                                        <td>{{ $item['order_id'] }}</td>
                                                        <td>
                                                            <span class="{{ getStatusClass($item['status']) }}">
                                                                {{ translateStatus($item['status']) }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div style="color: #6c757d; font-size: 0.8rem;">
                                                                {{ $formattedDateTime }}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="3" class="empty-table">Nenhum status encontrado</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabela Ordens ID 22 (IT Team) -->
                    <div class="col-md-4">
                        <!-- Barra de Alerta IT Team -->
                        <div class="it-team-alert" id="it-team-alert" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="it-team-alert-text">ATENÇÃO: Existem ordens pendentes na equipe de TI!</span>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    IT Team Orders (ID 22)
                                    <span class="badge bg-secondary" id="count-22" style="font-size: 0.8rem;">
                                        {{ isset($ordersData) ? ($ordersData['total_22'] ?? 0) : 0 }}
                                    </span>
                                </h5>
                                <div class="table-container" style="max-height: 350px; overflow-y: auto;">
                                    <table class="table table-dark table-hover table-sm">
                                        <thead class="sticky-top">
                                            <tr>
                                                <th>Created</th>
                                                <th>User</th>
                                                <th>Order ID</th>
                                                <th>Channel</th>
                                                <th>Qty</th>
                                                <th>Price</th>
                                                <th>Status</th>
                                                <th>JSON</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orders-22-tbody">
                                            @if(isset($ordersData) && !empty($ordersData['orders_22']))
                                                @foreach($ordersData['orders_22'] as $order)
                                                    <tr>
                                                        <td>{{ \Carbon\Carbon::parse($order['order_created_at'])->format('d/m H:i') }}</td>
                                                        <td>{{ $order['user_id'] }}</td>
                                                        <td>{{ $order['order_id'] }}</td>
                                                        <td>{{ $order['channel'] }}</td>
                                                        <td>{{ $order['product_quantity'] }}</td>
                                                        <td>€{{ number_format($order['price'], 2) }}</td>
                                                        <td>
                                                            <span class="{{ getStatusClass($order['status']) }}">
                                                                {{ translateStatus($order['status']) }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('{{ $order['order_id'] }}')">
                                                                <i class="fas fa-code"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>





                    <!-- Tabela Ordens ID 33 (Produção) -->
                    <div class="col-md-5">
                        <!-- Barra de Alerta Transfer Orders -->
                        <div class="transfer-alert" id="transfer-alert" style="display: none;">
                            <i class="fas fa-clock"></i>
                            <span id="transfer-alert-text">ATENÇÃO: Ordens em WAITING ACCEPT há mais de 2 horas!</span>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">
                                        Orders In Transfer (ID 33)
                                        <span class="badge bg-primary" id="count-33" style="font-size: 0.8rem;">
                                            {{ isset($ordersData) ? $ordersData['total_33'] : 0 }}
                                        </span>
                                    </h5>
                                    <div class="btn-group" role="group" id="status-filter-buttons-33">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="filter-all-33" data-status="all">
                                            Todos
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" id="filter-waiting-acceptance-33" data-status="WAITING_ACCEPTANCE" style="display: none;">
                                            WAITING ACCEPT
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" id="filter-waiting-payment-33" data-status="WAITING_DEBIT_PAYMENT" style="display: none;">
                                            WAITING PAYMENT
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="filter-staging-33" data-status="STAGING" style="display: none;">
                                            STAGING
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" id="filter-created-33" data-status="CREATED" style="display: none;">
                                            CREATED
                                        </button>
                                    </div>
                                </div>
                                <div class="table-container">
                                    <table class="table table-dark table-hover orders-table">
                                        <thead>
                                            <tr>
                                                <th>Created</th>
                                                <th>User</th>
                                                <th>Order ID</th>
                                                <th>Channel</th>
                                                <th>Qty</th>
                                                <th>Price</th>
                                                <th>Status</th>
                                                <th>JSON</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orders-33-tbody">
                                            @if(isset($ordersData) && !empty($ordersData['orders_33']))
                                                @foreach($ordersData['orders_33'] as $order)
                                                    <tr>
                                                        <td>{{ \Carbon\Carbon::parse($order['order_created_at'])->format('d/m H:i') }}</td>
                                                        <td>{{ $order['user_id'] }}</td>
                                                        <td>{{ $order['order_id'] }}</td>
                                                        <td>{{ $order['channel'] }}</td>
                                                        <td>{{ $order['product_quantity'] }}</td>
                                                        <td>€{{ number_format($order['price'], 2) }}</td>
                                                        <td>
                                                            <span class="{{ getStatusClass($order['status']) }}">
                                                                {{ $order['status'] }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('{{ $order['order_id'] }}')">
                                                                <i class="fas fa-code"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabela de Marketplaces -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    🏪 Lista de Marketplaces
                                    <span class="badge bg-info" id="marketplaces-count" style="font-size: 0.8rem;">
                                        0
                                    </span>
                                </h5>
                                <div class="table-container">
                                    <table class="table table-dark table-hover orders-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Nome</th>
                                                <th>País</th>
                                                <th>Shop State</th>
                                                <th>Offers</th>
                                                <th>Orders</th>
                                            </tr>
                                        </thead>
                                        <tbody id="marketplaces-tbody">
                                            <tr>
                                                <td colspan="6" class="text-center">Carregando marketplaces...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para atualizar os relógios
        function updateClocks() {
            const now = new Date();

            // Portugal (UTC+1)
            const portugalTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Lisbon"}));
            document.getElementById('portugal-time').textContent = portugalTime.toLocaleTimeString();

            // Espanha (UTC+1)
            const spainTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Madrid"}));
            document.getElementById('spain-time').textContent = spainTime.toLocaleTimeString();

            // França (UTC+1)
            const franceTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Paris"}));
            document.getElementById('france-time').textContent = franceTime.toLocaleTimeString();

            // Alemanha (UTC+1)
            const germanyTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Berlin"}));
            document.getElementById('germany-time').textContent = germanyTime.toLocaleTimeString();
        }

        // Atualizar relógios a cada segundo
        setInterval(updateClocks, 1000);
        updateClocks(); // Chamada inicial

        // Código do formulário removido - não existe dashboard-form na página

        // Função para carregar novas ordens
        function loadNovasOrdens(isManual = false) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] 🔄 ATUALIZANDO TABELAS...`);

            // Se for atualização manual, resetar countdown
            if (isManual) {
                resetCountdown();
            }

            fetch('{{ route("dashboard.orders") }}')
                .then(response => response.json())
                .then(data => {
                    console.log(`[${timestamp}] 📊 RESPOSTA COMPLETA:`, data);

                    if (data.success) {
                        console.log(`[${timestamp}] ✅ Dados recebidos:`, {
                            orders_33: data.data?.orders_33?.length || 0,
                            orders_25: data.data?.orders_25?.length || 0,
                            db_orders: data.db_orders?.length || 0,
                            status_timeline: data.status_timeline?.length || 0,
                            total_33: data.data?.total_33 || 0,
                            total_25: data.data?.total_25 || 0
                        });

                        // Verificar se os elementos das tabelas existem
                        const tbody33 = document.getElementById('orders-33-tbody');
                        const tbody25 = document.getElementById('orders-25-tbody');
                        const tbodyDb = document.getElementById('orders-db-tbody');
                        const tbodyTimeline = document.getElementById('status-timeline-tbody');

                        console.log(`[${timestamp}] 🔍 ELEMENTOS ENCONTRADOS:`, {
                            tbody33: !!tbody33,
                            tbody25: !!tbody25,
                            tbodyDb: !!tbodyDb,
                            tbodyTimeline: !!tbodyTimeline
                        });

                        if (data.data) {
                            updateOrdersTables(data.data);
                            updateStatistics(data.data);
                        } else {
                            console.error('❌ data.data está vazio!');
                        }

                        updateDbOrdersTable(data.db_orders || []);
                        updateStatusTimeline(data.status_timeline || []);
                        updateSalesStatistics(data.sales_stats || {});
                        updateMarketplaces(data.marketplaces || []);

                        console.log(`[${timestamp}] ✅ TODAS AS TABELAS ATUALIZADAS! Próxima atualização em 10 segundos.`);
                    } else {
                        console.error('❌ Erro ao carregar ordens:', data.message);
                    }
                })
                .catch(error => {
                    console.error('❌ Erro na requisição:', error);
                });
        }

        // Função para obter classe CSS do status
        function getStatusClass(status) {
            switch(status.toLowerCase()) {
                case 'waiting_acceptance':
                case 'waitingacceptance':
                    return 'status-waiting';
                case 'waiting_debit_payment':
                case 'waiting_debit':
                    return 'status-payment';
                case 'staging':
                    return 'status-staging';
                case 'created':
                    return 'status-created';
                default:
                    return 'status-default';
            }
        }

        // Função para traduzir status para inglês
        function translateStatus(status) {
            switch(status.toLowerCase()) {
                case 'waiting_acceptance':
                case 'waitingacceptance':
                    return 'WAITING ACCEPT';
                case 'waiting_debit_payment':
                case 'waiting_debit':
                    return 'WAITING PAYMENT';
                case 'staging':
                    return 'STAGING';
                case 'created':
                    return 'CREATED';
                default:
                    return status.toUpperCase();
            }
        }

        // Função para atualizar as tabelas de ordens
        function updateOrdersTables(ordersData) {
            console.log('📊 Atualizando tabelas de ordens...', ordersData);

            if (!ordersData) {
                console.error('❌ ordersData está vazio!');
                return;
            }

            // Detectar novas ordens de suporte (ID 25)
            detectNewSupportOrders(ordersData.orders_25 || []);

            // Detectar novas ordens de transfer (ID 33)
            detectNewTransferOrders(ordersData.orders_33 || []);

            // Detectar novas ordens
            const newOrders33 = detectNewOrders(ordersData.orders_33 || [], previousOrders33);
            const newOrders25 = detectNewOrders(ordersData.orders_25 || [], previousOrders25);

            console.log('🔍 Dados das ordens:', {
                orders_33_count: ordersData.orders_33?.length || 0,
                orders_25_count: ordersData.orders_25?.length || 0,
                orders_22_count: ordersData.orders_22?.length || 0,
                orders_33_sample: ordersData.orders_33?.[0] || 'nenhuma',
                orders_25_sample: ordersData.orders_25?.[0] || 'nenhuma',
                orders_22_sample: ordersData.orders_22?.[0] || 'nenhuma'
            });

            // Atualizar tabela ID 33
            const tbody33 = document.getElementById('orders-33-tbody');
            if (!tbody33) {
                console.error('❌ Elemento orders-33-tbody não encontrado!');
                return;
            }
            tbody33.innerHTML = '';
            console.log('🔄 Limpando tabela ID 33...');

            if (ordersData.orders_33 && ordersData.orders_33.length > 0) {
                ordersData.orders_33.forEach(order => {
                    const row = document.createElement('tr');
                    const orderDate = new Date(order.order_created_at);
                    const dateString = orderDate.toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // Verificar se é nova ordem
                    const isNewOrder = newOrders33.some(newOrder => newOrder.order_id === order.order_id);
                    if (isNewOrder) {
                        row.classList.add('new-order');
                    }

                    row.innerHTML = `
                        <td>${dateString}</td>
                        <td>${order.user_id}</td>
                        <td>${order.order_id}</td>
                        <td>${order.channel}</td>
                        <td>${order.product_quantity}</td>
                        <td>€${parseFloat(order.price).toFixed(2)}</td>
                        <td><span class="${getStatusClass(order.status)}">${translateStatus(order.status)}</span></td>
                        <td>
                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('${order.order_id}')">
                                <i class="fas fa-code"></i>
                            </button>
                        </td>
                    `;
                    tbody33.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>';
                tbody33.appendChild(row);
            }

            // Atualizar tabela ID 22
            const tbody22 = document.getElementById('orders-22-tbody');
            if (!tbody22) {
                console.error('❌ Elemento orders-22-tbody não encontrado!');
                return;
            }
            tbody22.innerHTML = '';
            console.log('🔄 Limpando tabela ID 22...');

            if (ordersData.orders_22 && ordersData.orders_22.length > 0) {
                ordersData.orders_22.forEach(order => {
                    const row = document.createElement('tr');
                    const orderDate = new Date(order.order_created_at);
                    const dateString = orderDate.toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    row.innerHTML = `
                        <td>${dateString}</td>
                        <td>${order.user_id}</td>
                        <td>${order.order_id}</td>
                        <td>${order.channel}</td>
                        <td>${order.product_quantity}</td>
                        <td>€${parseFloat(order.price).toFixed(2)}</td>
                        <td><span class="${getStatusClass(order.status)}">${translateStatus(order.status)}</span></td>
                        <td>
                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('${order.order_id}')">
                                <i class="fas fa-code"></i>
                            </button>
                        </td>
                    `;
                    tbody22.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>';
                tbody22.appendChild(row);
            }

            // Atualizar tabela ID 25
            const tbody25 = document.getElementById('orders-25-tbody');
            if (!tbody25) {
                console.error('❌ Elemento orders-25-tbody não encontrado!');
                return;
            }
            tbody25.innerHTML = '';
            console.log('🔄 Limpando tabela ID 25...');

            if (ordersData.orders_25 && ordersData.orders_25.length > 0) {
                ordersData.orders_25.forEach(order => {
                    const row = document.createElement('tr');
                    const orderDate = new Date(order.order_created_at);
                    const dateString = orderDate.toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // Verificar se é nova ordem
                    const isNewOrder = newOrders25.some(newOrder => newOrder.order_id === order.order_id);
                    if (isNewOrder) {
                        row.classList.add('new-order');
                    }

                    row.innerHTML = `
                        <td>${dateString}</td>
                        <td>${order.user_id}</td>
                        <td>${order.order_id}</td>
                        <td>${order.channel}</td>
                        <td>${order.product_quantity}</td>
                        <td>€${parseFloat(order.price).toFixed(2)}</td>
                        <td><span class="${getStatusClass(order.status)}">${translateStatus(order.status)}</span></td>
                        <td>
                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('${order.order_id}')">
                                <i class="fas fa-code"></i>
                            </button>
                        </td>
                    `;
                    tbody25.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="empty-table">Nenhuma ordem encontrada</td>';
                tbody25.appendChild(row);
            }

            // Atualizar arrays de ordens anteriores
            previousOrders33 = ordersData.orders_33 || [];
            previousOrders25 = ordersData.orders_25 || [];

            // Verificar alerta de Transfer Orders (ID 33)
            updateTransferAlert(ordersData.orders_33 || []);

            // Atualizar botões de filtro baseado nos status disponíveis
            updateFilterButtons(ordersData.orders_33 || []);
        }

        // Variável para armazenar dados originais da tabela 33
        let originalOrders33 = [];

        // Função para atualizar botões de filtro
        function updateFilterButtons(orders) {
            // Armazenar dados originais
            originalOrders33 = orders;

            // Obter status únicos
            const uniqueStatuses = [...new Set(orders.map(order => order.status))];

            console.log('🔘 Status únicos encontrados:', uniqueStatuses);

            // Mostrar/esconder botões baseado nos status disponíveis
            const buttons = {
                'WAITING_ACCEPTANCE': document.getElementById('filter-waiting-acceptance-33'),
                'WAITING_DEBIT_PAYMENT': document.getElementById('filter-waiting-payment-33'),
                'STAGING': document.getElementById('filter-staging-33'),
                'CREATED': document.getElementById('filter-created-33')
            };

            // Esconder todos os botões primeiro
            Object.values(buttons).forEach(btn => {
                if (btn) btn.style.display = 'none';
            });

            // Mostrar apenas os botões dos status que existem
            uniqueStatuses.forEach(status => {
                if (buttons[status]) {
                    buttons[status].style.display = 'inline-block';
                }
            });
        }

        // Função para filtrar tabela por status
        function filterTable33ByStatus(status) {
            console.log('🔍 Filtrando tabela 33 por status:', status);

            const tbody = document.getElementById('orders-33-tbody');
            tbody.innerHTML = '';

            let filteredOrders = originalOrders33;

            // Filtrar se não for "all"
            if (status !== 'all') {
                filteredOrders = originalOrders33.filter(order => order.status === status);
            }

            console.log(`📊 Mostrando ${filteredOrders.length} de ${originalOrders33.length} ordens`);

            // Renderizar ordens filtradas
            if (filteredOrders.length > 0) {
                filteredOrders.forEach(order => {
                    const row = document.createElement('tr');
                    const orderDate = new Date(order.order_created_at);
                    const dateString = orderDate.toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    row.innerHTML = `
                        <td>${dateString}</td>
                        <td>${order.user_id}</td>
                        <td>${order.order_id}</td>
                        <td>${order.channel}</td>
                        <td>${order.product_quantity}</td>
                        <td>€${parseFloat(order.price).toFixed(2)}</td>
                        <td><span class="${getStatusClass(order.status)}">${translateStatus(order.status)}</span></td>
                        <td>
                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetailsModal('${order.order_id}')">
                                <i class="fas fa-code"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="empty-table">Nenhuma ordem encontrada para este status</td>';
                tbody.appendChild(row);
            }

            // Atualizar contador
            document.getElementById('count-33').textContent = filteredOrders.length;

            // Atualizar estado visual dos botões
            updateFilterButtonsState(status);
        }

        // Função para atualizar estado visual dos botões
        function updateFilterButtonsState(activeStatus) {
            const buttons = document.querySelectorAll('#status-filter-buttons-33 button');

            buttons.forEach(btn => {
                const btnStatus = btn.getAttribute('data-status');
                if (btnStatus === activeStatus) {
                    // Botão ativo
                    btn.classList.remove('btn-outline-secondary', 'btn-outline-warning', 'btn-outline-info', 'btn-outline-primary', 'btn-outline-success');

                    // Aplicar cor sólida baseada no status
                    switch(btnStatus) {
                        case 'all':
                            btn.classList.add('btn-secondary');
                            break;
                        case 'WAITING_ACCEPTANCE':
                            btn.classList.add('btn-warning');
                            break;
                        case 'WAITING_DEBIT_PAYMENT':
                            btn.classList.add('btn-info');
                            break;
                        case 'STAGING':
                            btn.classList.add('btn-primary');
                            break;
                        case 'CREATED':
                            btn.classList.add('btn-success');
                            break;
                    }
                } else {
                    // Botão inativo - voltar para outline
                    btn.classList.remove('btn-secondary', 'btn-warning', 'btn-info', 'btn-primary', 'btn-success');

                    switch(btn.getAttribute('data-status')) {
                        case 'all':
                            btn.classList.add('btn-outline-secondary');
                            break;
                        case 'WAITING_ACCEPTANCE':
                            btn.classList.add('btn-outline-warning');
                            break;
                        case 'WAITING_DEBIT_PAYMENT':
                            btn.classList.add('btn-outline-info');
                            break;
                        case 'STAGING':
                            btn.classList.add('btn-outline-primary');
                            break;
                        case 'CREATED':
                            btn.classList.add('btn-outline-success');
                            break;
                    }
                }
            });
        }

        // Variáveis para controle de ordens anteriores
        let previousOrders33 = [];
        let previousOrders25 = [];

        // Variáveis para controle do countdown - AUMENTADO PARA 30 SEGUNDOS
        let countdownInterval;
        let countdownSeconds = 30;

        // Variáveis para controle do status timeline
        let timelineInterval;
        let previousTimeline = [];
        let previousTimelineIds = new Set(); // Controle de IDs já processados
        let timelineInitialized = false; // Flag para primeira carga

        // Variáveis para controle das ordens de suporte
        let previousSupportOrderIds = new Set(); // Controle de IDs de suporte já processados
        let supportOrdersInitialized = false; // Flag para primeira carga de suporte

        // Variáveis para controle das ordens de transfer (ID 33)
        let previousTransferOrderIds = new Set(); // Controle de IDs de transfer já processados
        let transferOrdersInitialized = false; // Flag para primeira carga de transfer

        // Função para atualizar estatísticas
        function updateStatistics(ordersData) {
            // Atualizar quadro único de estatísticas
            document.getElementById('total-ativos').textContent = ordersData.total_33 + ordersData.total_25;
            document.getElementById('greens-count').textContent = ordersData.total_33;
            document.getElementById('reds-count').textContent = ordersData.total_25;
            document.getElementById('last-hour-greens').textContent = ordersData.waiting_acceptance ? ordersData.waiting_acceptance.length : 0;
            document.getElementById('last-hour-reds').textContent = ordersData.waiting_debit_payment ? ordersData.waiting_debit_payment.length : 0;
            document.getElementById('last-hour-m2-positive').textContent = ordersData.staging ? ordersData.staging.length : 0;
            document.getElementById('last-hour-m2-negative').textContent = ordersData.created ? ordersData.created.length : 0;

            // Atualizar badges das tabelas
            document.getElementById('count-33').textContent = ordersData.total_33;
            document.getElementById('count-25').textContent = ordersData.total_25;
            document.getElementById('count-22').textContent = ordersData.total_22 || 0;

            // Controlar barra de alerta IT Team (ID 22)
            updateITTeamAlert(ordersData.total_22 || 0);
        }

        // Função para controlar a barra de alerta IT Team
        function updateITTeamAlert(total22) {
            const alertElement = document.getElementById('it-team-alert');
            const alertText = document.getElementById('it-team-alert-text');

            if (total22 > 0) {
                // Mostrar alerta
                alertElement.style.display = 'block';
                alertText.textContent = `⚠️ ATENÇÃO: ${total22} ordem${total22 > 1 ? 's' : ''} pendente${total22 > 1 ? 's' : ''} na equipe de TI!`;
                console.log(`🚨 Alerta IT Team ativado: ${total22} ordens`);
            } else {
                // Esconder alerta
                alertElement.style.display = 'none';
                console.log('✅ Alerta IT Team desativado: sem ordens');
            }
        }

        // Função para controlar a barra de alerta Transfer Orders
        function updateTransferAlert(orders33) {
            const alertElement = document.getElementById('transfer-alert');
            const alertText = document.getElementById('transfer-alert-text');

            if (!orders33 || orders33.length === 0) {
                alertElement.style.display = 'none';
                console.log('🔍 Transfer Alert: Nenhuma ordem ID 33 encontrada');
                return;
            }

            console.log(`🔍 Transfer Alert: Verificando ${orders33.length} ordens ID 33`);

            // Verificar ordens em WAITING ACCEPT com mais de 2 horas
            const now = new Date();
            const twoHoursAgo = new Date(now.getTime() - (2 * 60 * 60 * 1000)); // 2 horas atrás

            let expiredOrders = [];
            let waitingAcceptOrders = [];

            orders33.forEach(order => {
                // Normalizar status para comparação
                const originalStatus = order.status;
                const normalizedStatus = originalStatus.toUpperCase().replace(/[_\s-]/g, '');

                // Verificar se está em WAITING ACCEPT (todas as variações possíveis)
                const isWaitingAccept = (
                    normalizedStatus.includes('WAITINGACCEPT') ||
                    normalizedStatus === 'WAITINGACCEPTANCE' ||
                    originalStatus.toUpperCase() === 'WAITING ACCEPT' ||
                    originalStatus.toUpperCase() === 'WAITING_ACCEPT' ||
                    originalStatus.toUpperCase() === 'WAITING-ACCEPT' ||
                    originalStatus.toUpperCase().includes('WAITING') && originalStatus.toUpperCase().includes('ACCEPT')
                );

                if (isWaitingAccept) {
                    waitingAcceptOrders.push(order);

                    // Verificar se passou de 2 horas
                    const orderDate = new Date(order.order_created_at);
                    const hoursElapsed = Math.floor((now - orderDate) / (1000 * 60 * 60));

                    console.log(`⏰ Ordem ${order.order_id} em WAITING ACCEPT: criada ${order.order_created_at}, ${hoursElapsed}h atrás`);

                    if (orderDate < twoHoursAgo) {
                        expiredOrders.push({
                            order_id: order.order_id,
                            created_at: order.order_created_at,
                            hours_elapsed: hoursElapsed,
                            status: originalStatus
                        });
                        console.log(`🚨 Ordem ${order.order_id} EXPIRADA: ${hoursElapsed}h`);
                    }
                }
            });

            console.log(`📊 Resultado da verificação:`);
            console.log(`   - Total de ordens ID 33: ${orders33.length}`);
            console.log(`   - Ordens em WAITING ACCEPT: ${waitingAcceptOrders.length}`);
            console.log(`   - Ordens expiradas (>2h): ${expiredOrders.length}`);

            if (expiredOrders.length > 0) {
                // Calcular tempo máximo de espera
                const maxHours = Math.max(...expiredOrders.map(order => order.hours_elapsed));

                // Mostrar alerta com informações detalhadas
                alertElement.style.display = 'block';
                alertText.textContent = `⚠️ ATENÇÃO: ${expiredOrders.length} ordem${expiredOrders.length > 1 ? 's' : ''} em WAITING ACCEPT há mais de 2 horas! (máx: ${maxHours}h)`;

                console.log(`🚨 Alerta Transfer ativado: ${expiredOrders.length} ordens expiradas de ${waitingAcceptOrders.length} em WAITING ACCEPT`);
                console.table(expiredOrders);
            } else {
                // Esconder alerta
                alertElement.style.display = 'none';
                if (waitingAcceptOrders.length > 0) {
                    console.log(`✅ Alerta Transfer desativado: ${waitingAcceptOrders.length} ordens em WAITING ACCEPT, mas todas dentro do prazo`);
                } else {
                    console.log('✅ Alerta Transfer desativado: nenhuma ordem em WAITING ACCEPT');
                }
            }
        }

        // Função para atualizar estatísticas de vendas
        function updateSalesStatistics(salesStats) {
            console.log('💰 Atualizando estatísticas de vendas:', salesStats);

            if (salesStats && salesStats.mes) {
                const monthTotal = parseFloat(salesStats.mes.total_liquido || 0).toFixed(2);
                const monthQty = salesStats.mes.quantidade || 0;
                document.getElementById('sales-month-display').innerHTML =
                    `<span style="color: #6c757d; font-size: 0.8em;">€</span>${monthTotal} <span style="color: #6c757d; font-size: 0.8em;">(${monthQty})</span>`;
            }

            if (salesStats && salesStats.dia) {
                const dayTotal = parseFloat(salesStats.dia.total_liquido || 0).toFixed(2);
                const dayQty = salesStats.dia.quantidade || 0;
                document.getElementById('sales-day-display').innerHTML =
                    `<span style="color: #6c757d; font-size: 0.8em;">€</span>${dayTotal} <span style="color: #6c757d; font-size: 0.8em;">(${dayQty})</span>`;
            }
        }

        // Função para atualizar marketplaces (dados vêm do backend)
        function updateMarketplaces(marketplacesResponse) {
            console.log('🏪 Resposta completa dos marketplaces:', marketplacesResponse);

            // Extrair o array de dados da resposta
            let marketplacesData = [];

            if (marketplacesResponse && marketplacesResponse.success && marketplacesResponse.data) {
                marketplacesData = marketplacesResponse.data;
                console.log('🏪 Array de marketplaces extraído:', marketplacesData);
            } else if (Array.isArray(marketplacesResponse)) {
                marketplacesData = marketplacesResponse;
                console.log('🏪 Marketplaces já é array:', marketplacesData);
            } else {
                console.log('🏪 Formato de dados não reconhecido:', marketplacesResponse);
            }

            updateMarketplacesTable(marketplacesData);
        }

        // Função para atualizar tabela de marketplaces
        function updateMarketplacesTable(marketplaces) {
            const tbody = document.getElementById('marketplaces-tbody');
            const countElement = document.getElementById('marketplaces-count');

            if (!tbody) {
                console.error('❌ Elemento marketplaces-tbody não encontrado');
                return;
            }

            // Limpar tabela
            tbody.innerHTML = '';

            // Verificar se marketplaces é um array válido
            if (!marketplaces || !Array.isArray(marketplaces) || marketplaces.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">Nenhum marketplace encontrado</td></tr>';
                if (countElement) countElement.textContent = '0';
                console.log('⚠️ Marketplaces vazio ou inválido:', marketplaces);
                return;
            }

            // Atualizar contador
            if (countElement) {
                countElement.textContent = marketplaces.length;
            }

            // Adicionar cada marketplace à tabela
            marketplaces.forEach(marketplace => {
                const row = document.createElement('tr');

                // Badge para shop state
                const shopStateBadge = getShopStateBadge(marketplace.account_shop_state || 'unknown');

                row.innerHTML = `
                    <td>${marketplace.id || '-'}</td>
                    <td><strong>${marketplace.name || 'N/A'}</strong></td>
                    <td>${marketplace.country || '-'}</td>
                    <td>${shopStateBadge}</td>
                    <td>${formatNumber(marketplace.account_offers_count) || '0'}</td>
                    <td>${marketplace.account_orders_count || '0'}</td>
                `;

                tbody.appendChild(row);
            });

            console.log(`✅ ${marketplaces.length} marketplaces carregados na tabela`);
        }

        // Função para obter badge de shop state
        function getShopStateBadge(shopState) {
            const stateLower = shopState.toLowerCase();

            switch(stateLower) {
                case 'open':
                    return '<span class="badge bg-success">OPEN</span>';
                case 'closed':
                    return '<span class="badge bg-danger">CLOSED</span>';
                case 'suspended':
                    return '<span class="badge bg-warning">SUSPENDED</span>';
                case 'pending':
                    return '<span class="badge bg-info">PENDING</span>';
                default:
                    return `<span class="badge bg-secondary">${shopState}</span>`;
            }
        }

        // Função para formatar números grandes
        function formatNumber(num) {
            if (!num || num === 0) return '0';

            const number = parseInt(num);
            if (number >= 1000000) {
                return (number / 1000000).toFixed(1) + 'M';
            } else if (number >= 1000) {
                return (number / 1000).toFixed(1) + 'K';
            }
            return number.toLocaleString();
        }

        // Função para atualizar tabela de ordens do banco de dados
        function updateDbOrdersTable(dbOrders) {
            console.log('🗄️ Atualizando tabela da base de dados...');
            const tbody = document.getElementById('orders-db-tbody');
            tbody.innerHTML = '';

            if (dbOrders && dbOrders.length > 0) {
                dbOrders.forEach(order => {
                    const row = document.createElement('tr');
                    const orderDate = new Date(order.created_at);
                    const dateString = orderDate.toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    row.innerHTML = `
                        <td>${dateString}</td>
                        <td>${order.user_id}</td>
                        <td>${order.order_id}</td>
                        <td>${order.channel}-${order.country}</td>
                        <td>${order.product_quantity}</td>
                        <td>€${parseFloat(order.price).toFixed(2)}</td>
                        <td>€${parseFloat(order.commission).toFixed(2)}</td>
                        <td><span class="${getStatusClass(order.status)}">${translateStatus(order.status)}</span></td>
                    `;
                    tbody.appendChild(row);
                });

                // Atualizar contador
                document.getElementById('count-db').textContent = dbOrders.length;
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8" class="empty-table">Nenhuma ordem encontrada na base de dados</td>';
                tbody.appendChild(row);

                // Atualizar contador
                document.getElementById('count-db').textContent = '0';
            }
        }

        // Função para iniciar countdown
        function startCountdown() {
            console.log('🔄 Iniciando countdown de 30 segundos...');

            // Limpar interval anterior se existir
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }

            countdownSeconds = 30;
            updateCountdownDisplay();

            countdownInterval = setInterval(() => {
                countdownSeconds--;
                updateCountdownDisplay();

                if (countdownSeconds <= 0) {
                    clearInterval(countdownInterval);

                    // Mostrar indicador de atualização
                    const timer = document.getElementById('countdown-timer');
                    if (timer) {
                        timer.textContent = '🔄 Updating...';
                        timer.className = 'badge bg-info me-2';
                        timer.style.animation = 'pulse 0.3s infinite';
                    }

                    loadNovasOrdens();
                    startCountdown(); // Reiniciar o countdown
                }
            }, 1000);
        }

        // Função para atualizar display do countdown
        function updateCountdownDisplay() {
            const timer = document.getElementById('countdown-timer');
            if (timer) {
                // Mostrar countdown com formato mais visível
                timer.textContent = `⏱️ ${countdownSeconds}s`;

                // Mudar cor baseado no tempo restante com animação
                if (countdownSeconds <= 3) {
                    timer.className = 'badge bg-danger me-2';
                    timer.style.animation = 'pulse 0.5s infinite';
                } else if (countdownSeconds <= 5) {
                    timer.className = 'badge bg-warning me-2';
                    timer.style.animation = 'pulse 1s infinite';
                } else {
                    timer.className = 'badge bg-success me-2';
                    timer.style.animation = 'pulse 2s infinite';
                }

                // Log no console para debug
                console.log(`⏱️ Próxima atualização em: ${countdownSeconds} segundos`);
            } else {
                console.error('❌ Elemento countdown-timer não encontrado!');
            }
        }

        // Função para resetar countdown (quando atualização manual)
        function resetCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            startCountdown();
        }

        // Função para atualizar tabela de status timeline (modo normal)
        function updateStatusTimeline(timelineData) {
            console.log('⏰ Atualizando status timeline...');
            // Só atualiza se não estiver em modo busca
            if (document.getElementById('countdown-timer').textContent === 'BUSCA') {
                return; // Não atualizar durante busca
            }

            // Detectar novas entradas no timeline
            detectNewTimelineEntries(timelineData);

            updateTimelineTable(timelineData);
        }

        // Função para formatar data/hora para o formato "08.06 - 13:10"
        function formatDateTime(data, hora) {
            try {
                // Se data já está no formato desejado, usar diretamente
                if (data && data.includes('.') && data.length <= 10) {
                    return `${data} - ${hora}`;
                }

                // Se data está no formato "2025-06-08 13:10:13" ou similar
                let dateStr = data;
                let timeStr = hora;

                // Se hora não está separada, extrair da data completa
                if (data && data.includes(' ') && !hora) {
                    const parts = data.split(' ');
                    dateStr = parts[0];
                    timeStr = parts[1];
                }

                // Converter data de "2025-06-08" para "08.06"
                if (dateStr && dateStr.includes('-')) {
                    const dateParts = dateStr.split('-');
                    if (dateParts.length >= 3) {
                        const day = dateParts[2];
                        const month = dateParts[1];
                        dateStr = `${day}.${month}`;
                    }
                }

                // Converter hora de "13:10:13" para "13:10"
                if (timeStr && timeStr.includes(':')) {
                    const timeParts = timeStr.split(':');
                    if (timeParts.length >= 2) {
                        timeStr = `${timeParts[0]}:${timeParts[1]}`;
                    }
                }

                return `${dateStr} - ${timeStr}`;
            } catch (error) {
                console.error('Erro ao formatar data/hora:', error);
                return `${data} ${hora}`;
            }
        }

        // Função para atualizar tabela timeline
        function updateTimelineTable(data) {
            const tbody = document.getElementById('status-timeline-tbody');
            tbody.innerHTML = '';

            if (data && data.length > 0) {
                data.forEach(item => {
                    const row = document.createElement('tr');
                    const formattedDateTime = formatDateTime(item.data, item.hora);

                    row.innerHTML = `
                        <td>${item.order_id}</td>
                        <td><span class="${getStatusClass(item.status)}">${translateStatus(item.status)}</span></td>
                        <td>
                            <div style="color: #6c757d; font-size: 0.8rem;">
                                ${formattedDateTime}
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                document.getElementById('count-timeline').textContent = data.length;
            } else {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="3" class="empty-table">Nenhum registro encontrado</td>';
                tbody.appendChild(row);

                document.getElementById('count-timeline').textContent = '0';
            }
        }

        // Função para mostrar notificação do timeline
        function showTimelineNotification(entry) {
            // Traduzir status para português
            const statusTranslations = {
                'WAITING_ACCEPTANCE': 'Aguardando Aceitação',
                'WAITING_DEBIT_PAYMENT': 'Aguardando Pagamento',
                'STAGING': 'Em Preparação',
                'CREATED': 'Criado'
            };

            const translatedStatus = statusTranslations[entry.status] || entry.status;
            const message = `Order ${entry.order_id} recebeu status: ${translatedStatus}`;

            console.log('🔔 Mostrando notificação:', message);

            // Criar elemento de notificação
            const notification = document.createElement('div');
            notification.className = 'timeline-notification';
            notification.innerHTML = `
                <div class="timeline-notification-content">
                    <div class="timeline-notification-header">
                        <i class="fas fa-clock text-primary"></i>
                        <span class="timeline-notification-title">Nova Atualização</span>
                        <button class="timeline-notification-close">&times;</button>
                    </div>
                    <div class="timeline-notification-body">
                        <strong>Order ID:</strong> ${entry.order_id}<br>
                        <strong>Status:</strong> <span class="status-badge">${translatedStatus}</span><br>
                        <strong>Canal:</strong> ${entry.channel} | <strong>País:</strong> ${entry.country}
                    </div>
                </div>
            `;

            // Adicionar estilos inline para a notificação
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
                z-index: 9999;
                min-width: 320px;
                max-width: 400px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o conteúdo
            const content = notification.querySelector('.timeline-notification-content');
            content.style.cssText = `
                padding: 15px;
            `;

            // Estilos para o header
            const header = notification.querySelector('.timeline-notification-header');
            header.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o título
            const title = notification.querySelector('.timeline-notification-title');
            title.style.cssText = `
                font-weight: bold;
                font-size: 14px;
                margin-left: 8px;
                flex-grow: 1;
            `;

            // Estilos para o botão fechar
            const closeBtn = notification.querySelector('.timeline-notification-close');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            `;

            // Estilos para o body
            const body = notification.querySelector('.timeline-notification-body');
            body.style.cssText = `
                font-size: 13px;
                line-height: 1.4;
            `;

            // Estilos para o badge de status
            const statusBadge = notification.querySelector('.status-badge');
            statusBadge.style.cssText = `
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
            `;

            // Adicionar ao body
            document.body.appendChild(notification);

            // Mostrar notificação com animação
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remover automaticamente após 6 segundos
            const autoRemoveTimeout = setTimeout(() => {
                hideTimelineNotification(notification);
            }, 6000);

            // Evento para fechar manualmente
            closeBtn.addEventListener('click', () => {
                clearTimeout(autoRemoveTimeout);
                hideTimelineNotification(notification);
            });

            // Hover effects
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            });

            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.backgroundColor = 'transparent';
            });
        }

        // Função para esconder notificação do timeline
        function hideTimelineNotification(notification) {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }

        // Função para mostrar notificação de ordem de suporte
        function showSupportOrderNotification(order) {
            // Traduzir status para português
            const statusTranslations = {
                'WAITING_ACCEPTANCE': 'Aguardando Aceitação',
                'WAITING_DEBIT_PAYMENT': 'Aguardando Pagamento',
                'STAGING': 'Em Preparação',
                'CREATED': 'Criado'
            };

            const translatedStatus = statusTranslations[order.status] || order.status;
            const message = `Nova ordem de suporte: ${order.order_id} - ${translatedStatus}`;

            console.log('🔔 Mostrando notificação de suporte:', message);

            // Criar elemento de notificação
            const notification = document.createElement('div');
            notification.className = 'support-notification';
            notification.innerHTML = `
                <div class="support-notification-content">
                    <div class="support-notification-header">
                        <i class="fas fa-headset text-warning"></i>
                        <span class="support-notification-title">Nova Ordem de Suporte</span>
                        <button class="support-notification-close">&times;</button>
                    </div>
                    <div class="support-notification-body">
                        <strong>Order ID:</strong> ${order.order_id}<br>
                        <strong>Status:</strong> <span class="status-badge">${translatedStatus}</span><br>
                        <strong>Canal:</strong> ${order.channel} | <strong>Preço:</strong> €${parseFloat(order.price).toFixed(2)}
                    </div>
                </div>
            `;

            // Adicionar estilos inline para a notificação (laranja escuro)
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: linear-gradient(135deg, #e67e22, #d35400);
                color: white;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(230, 126, 34, 0.3);
                z-index: 9999;
                min-width: 320px;
                max-width: 400px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o conteúdo
            const content = notification.querySelector('.support-notification-content');
            content.style.cssText = `
                padding: 15px;
            `;

            // Estilos para o header
            const header = notification.querySelector('.support-notification-header');
            header.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o título
            const title = notification.querySelector('.support-notification-title');
            title.style.cssText = `
                font-weight: bold;
                font-size: 14px;
                margin-left: 8px;
                flex-grow: 1;
            `;

            // Estilos para o botão fechar
            const closeBtn = notification.querySelector('.support-notification-close');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            `;

            // Estilos para o body
            const body = notification.querySelector('.support-notification-body');
            body.style.cssText = `
                font-size: 13px;
                line-height: 1.4;
            `;

            // Estilos para o badge de status
            const statusBadge = notification.querySelector('.status-badge');
            statusBadge.style.cssText = `
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
            `;

            // Adicionar ao body
            document.body.appendChild(notification);

            // Mostrar notificação com animação
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remover automaticamente após 6 segundos
            const autoRemoveTimeout = setTimeout(() => {
                hideSupportOrderNotification(notification);
            }, 6000);

            // Evento para fechar manualmente
            closeBtn.addEventListener('click', () => {
                clearTimeout(autoRemoveTimeout);
                hideSupportOrderNotification(notification);
            });

            // Hover effects
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            });

            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.backgroundColor = 'transparent';
            });
        }

        // Função para esconder notificação de ordem de suporte
        function hideSupportOrderNotification(notification) {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }

        // Função para mostrar notificação de ordem de transfer (verde)
        function showTransferOrderNotification(order) {
            // Traduzir status para português
            const statusTranslations = {
                'WAITING_ACCEPTANCE': 'Aguardando Aceitação',
                'WAITING_DEBIT_PAYMENT': 'Aguardando Pagamento',
                'STAGING': 'Em Preparação',
                'CREATED': 'Criado'
            };

            const translatedStatus = statusTranslations[order.status] || order.status;
            const message = `Nova ordem de transfer: ${order.order_id} - ${translatedStatus}`;

            console.log('🔔 Mostrando notificação de transfer:', message);

            // Criar elemento de notificação
            const notification = document.createElement('div');
            notification.className = 'transfer-notification';
            notification.innerHTML = `
                <div class="transfer-notification-content">
                    <div class="transfer-notification-header">
                        <i class="fas fa-exchange-alt text-success"></i>
                        <span class="transfer-notification-title">Nova Ordem de Transfer</span>
                        <button class="transfer-notification-close">&times;</button>
                    </div>
                    <div class="transfer-notification-body">
                        <strong>Order ID:</strong> ${order.order_id}<br>
                        <strong>Status:</strong> <span class="status-badge">${translatedStatus}</span><br>
                        <strong>Canal:</strong> ${order.channel} | <strong>Preço:</strong> €${parseFloat(order.price).toFixed(2)}
                    </div>
                </div>
            `;

            // Adicionar estilos inline para a notificação (verde)
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
                z-index: 9999;
                min-width: 320px;
                max-width: 400px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o conteúdo
            const content = notification.querySelector('.transfer-notification-content');
            content.style.cssText = `
                padding: 15px;
            `;

            // Estilos para o header
            const header = notification.querySelector('.transfer-notification-header');
            header.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            `;

            // Estilos para o título
            const title = notification.querySelector('.transfer-notification-title');
            title.style.cssText = `
                font-weight: bold;
                font-size: 14px;
                margin-left: 8px;
                flex-grow: 1;
            `;

            // Estilos para o botão fechar
            const closeBtn = notification.querySelector('.transfer-notification-close');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            `;

            // Estilos para o body
            const body = notification.querySelector('.transfer-notification-body');
            body.style.cssText = `
                font-size: 13px;
                line-height: 1.4;
            `;

            // Estilos para o badge de status
            const statusBadge = notification.querySelector('.status-badge');
            statusBadge.style.cssText = `
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: bold;
            `;

            // Adicionar ao body
            document.body.appendChild(notification);

            // Mostrar notificação com animação
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remover automaticamente após 6 segundos
            const autoRemoveTimeout = setTimeout(() => {
                hideTransferOrderNotification(notification);
            }, 6000);

            // Evento para fechar manualmente
            closeBtn.addEventListener('click', () => {
                clearTimeout(autoRemoveTimeout);
                hideTransferOrderNotification(notification);
            });

            // Hover effects
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            });

            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.backgroundColor = 'transparent';
            });
        }

        // Função para esconder notificação de ordem de transfer
        function hideTransferOrderNotification(notification) {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }

        // Função para mostrar notificação genérica (mantida para compatibilidade)
        function showNotification(message) {
            // Criar elemento de notificação
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-bell"></i>
                    <span>${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `;

            // Adicionar ao body
            document.body.appendChild(notification);

            // Mostrar notificação
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Remover automaticamente após 5 segundos
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);

            // Evento para fechar manualmente
            notification.querySelector('.notification-close').addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }

        // Função para detectar novas ordens
        function detectNewOrders(newOrders, previousOrders) {
            if (previousOrders.length === 0) return [];

            const previousIds = previousOrders.map(order => order.order_id);
            return newOrders.filter(order => !previousIds.includes(order.order_id));
        }

        // Função para detectar novas ordens de suporte
        function detectNewSupportOrders(supportOrders) {
            if (!supportOrders || supportOrders.length === 0) {
                return;
            }

            console.log('🔍 Verificando novas ordens de suporte...');

            // Na primeira carga, apenas registrar os IDs sem notificar
            if (!supportOrdersInitialized) {
                console.log('📋 Primeira carga das ordens de suporte - registrando IDs existentes');
                supportOrders.forEach(order => {
                    const orderId = `${order.order_id}_${order.status}_${order.order_created_at}`;
                    previousSupportOrderIds.add(orderId);
                });
                supportOrdersInitialized = true;
                console.log(`✅ ${supportOrders.length} ordens de suporte iniciais registradas`);
                return;
            }

            // Detectar novas ordens baseado no ID
            const newSupportOrders = [];

            supportOrders.forEach(order => {
                // Usar uma combinação de order_id + status + timestamp como ID único
                const orderId = `${order.order_id}_${order.status}_${order.order_created_at}`;

                if (!previousSupportOrderIds.has(orderId)) {
                    newSupportOrders.push(order);
                    previousSupportOrderIds.add(orderId);
                    console.log('🆕 Nova ordem de suporte detectada:', order);
                }
            });

            // Mostrar notificações para novas ordens (apenas após inicialização)
            if (newSupportOrders.length > 0) {
                console.log(`🔔 ${newSupportOrders.length} nova(s) ordem(ns) de suporte`);

                newSupportOrders.forEach((order, index) => {
                    // Delay entre notificações para não sobrepor
                    setTimeout(() => {
                        showSupportOrderNotification(order);
                    }, index * 1000); // 1 segundo entre cada notificação
                });
            }

            // Limitar o tamanho do Set para evitar crescimento infinito
            if (previousSupportOrderIds.size > 1000) {
                const idsArray = Array.from(previousSupportOrderIds);
                const recentIds = idsArray.slice(-500);
                previousSupportOrderIds = new Set(recentIds);
                console.log('🧹 Limpeza de IDs antigos das ordens de suporte');
            }
        }

        // Função para detectar novas ordens de transfer (ID 33)
        function detectNewTransferOrders(transferOrders) {
            if (!transferOrders || transferOrders.length === 0) {
                return;
            }

            console.log('🔍 Verificando novas ordens de transfer...');

            // Na primeira carga, apenas registrar os IDs sem notificar
            if (!transferOrdersInitialized) {
                console.log('📋 Primeira carga das ordens de transfer - registrando IDs existentes');
                transferOrders.forEach(order => {
                    const orderId = `${order.order_id}_${order.status}_${order.order_created_at}`;
                    previousTransferOrderIds.add(orderId);
                });
                transferOrdersInitialized = true;
                console.log(`✅ ${transferOrders.length} ordens de transfer iniciais registradas`);
                return;
            }

            // Detectar novas ordens baseado no ID
            const newTransferOrders = [];

            transferOrders.forEach(order => {
                // Usar uma combinação de order_id + status + timestamp como ID único
                const orderId = `${order.order_id}_${order.status}_${order.order_created_at}`;

                if (!previousTransferOrderIds.has(orderId)) {
                    newTransferOrders.push(order);
                    previousTransferOrderIds.add(orderId);
                    console.log('🆕 Nova ordem de transfer detectada:', order);
                }
            });

            // Mostrar notificações para novas ordens (apenas após inicialização)
            if (newTransferOrders.length > 0) {
                console.log(`🔔 ${newTransferOrders.length} nova(s) ordem(ns) de transfer`);

                newTransferOrders.forEach((order, index) => {
                    // Delay entre notificações para não sobrepor
                    setTimeout(() => {
                        showTransferOrderNotification(order);
                    }, index * 1000); // 1 segundo entre cada notificação
                });
            }

            // Limitar o tamanho do Set para evitar crescimento infinito
            if (previousTransferOrderIds.size > 1000) {
                const idsArray = Array.from(previousTransferOrderIds);
                const recentIds = idsArray.slice(-500);
                previousTransferOrderIds = new Set(recentIds);
                console.log('🧹 Limpeza de IDs antigos das ordens de transfer');
            }
        }

        // Função para detectar novas entradas no timeline
        function detectNewTimelineEntries(timelineData) {
            if (!timelineData || timelineData.length === 0) {
                return;
            }

            console.log('🔍 Verificando novas entradas no timeline...');

            // Na primeira carga, apenas registrar os IDs sem notificar
            if (!timelineInitialized) {
                console.log('📋 Primeira carga do timeline - registrando IDs existentes');
                timelineData.forEach(entry => {
                    const entryId = `${entry.order_id}_${entry.status}_${entry.data}_${entry.hora}`;
                    previousTimelineIds.add(entryId);
                });
                timelineInitialized = true;
                console.log(`✅ ${timelineData.length} entradas iniciais registradas no timeline`);
                return;
            }

            // Detectar novas entradas baseado no ID
            const newEntries = [];

            timelineData.forEach(entry => {
                // Usar uma combinação de order_id + status + timestamp como ID único
                const entryId = `${entry.order_id}_${entry.status}_${entry.data}_${entry.hora}`;

                if (!previousTimelineIds.has(entryId)) {
                    newEntries.push(entry);
                    previousTimelineIds.add(entryId);
                    console.log('🆕 Nova entrada detectada no timeline:', entry);
                }
            });

            // Mostrar notificações para novas entradas (apenas após inicialização)
            if (newEntries.length > 0) {
                console.log(`🔔 ${newEntries.length} nova(s) entrada(s) no timeline`);

                newEntries.forEach((entry, index) => {
                    // Delay entre notificações para não sobrepor
                    setTimeout(() => {
                        showTimelineNotification(entry);
                    }, index * 1000); // 1 segundo entre cada notificação
                });
            }

            // Limitar o tamanho do Set para evitar crescimento infinito
            // Manter apenas os últimos 1000 IDs
            if (previousTimelineIds.size > 1000) {
                const idsArray = Array.from(previousTimelineIds);
                const recentIds = idsArray.slice(-500); // Manter os 500 mais recentes
                previousTimelineIds = new Set(recentIds);
                console.log('🧹 Limpeza de IDs antigos do timeline');
            }
        }

        // Event listener para o botão de debug (com verificação)
        const toggleDebugBtn = document.getElementById('toggle-debug');
        if (toggleDebugBtn) {
            toggleDebugBtn.addEventListener('click', function() {
            const button = this;
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ...';
            button.disabled = true;

            fetch('{{ route("dashboard.toggle-debug") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const debugStatus = document.getElementById('debug-status');
                    debugStatus.textContent = data.debug_mode ? 'ON' : 'OFF';

                    // Atualizar dados após mudança de modo
                    setTimeout(() => {
                        loadNovasOrdens(true); // Atualização manual
                    }, 500);

                    // Mostrar mensagem de sucesso
                    const resultDiv = document.getElementById('form-result');
                    const messageSpan = document.getElementById('result-message');

                    resultDiv.className = 'mt-3 alert alert-success alert-sm';
                    messageSpan.innerHTML = data.message;
                    resultDiv.style.display = 'block';

                    setTimeout(() => {
                        resultDiv.style.display = 'none';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Erro ao alternar debug:', error);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
        }

        // Variável global para armazenar dados da ordem encontrada
        let foundOrderData = null;

        // Função para buscar ordem - VERSÃO SIMPLIFICADA
        function searchOrder() {
            console.log('🔍 INICIANDO BUSCA SIMPLES...');

            const orderNumber = document.getElementById('order-search-input').value.trim();
            const searchBtn = document.getElementById('search-order-btn');
            const resultDiv = document.getElementById('search-result');
            const messageSpan = document.getElementById('search-message');
            const detailsBtn = document.getElementById('show-details-btn');

            console.log('📝 Ordem digitada:', orderNumber);

            if (!orderNumber) {
                alert('Digite um número de ordem!');
                return;
            }

            // LOADING VISUAL MELHORADO
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-white"></i> <strong>BUSCANDO...</strong>';
            searchBtn.disabled = true;
            searchBtn.className = 'btn btn-warning';
            searchBtn.style.animation = 'pulse 1s infinite';

            messageSpan.textContent = '⏳ Buscando na base de dados...';
            messageSpan.className = 'text-warning ms-2 fw-bold';
            resultDiv.style.display = 'block';
            detailsBtn.style.display = 'none';

            // URL DIRETA PARA TESTE
            const url = '/dashboard/search-order';
            console.log('🌐 URL da requisição:', url);

            // REQUISIÇÃO SIMPLIFICADA
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    order_number: orderNumber
                })
            })
            .then(response => {
                console.log('📡 Status da resposta:', response.status);
                console.log('📡 Headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`Erro HTTP: ${response.status}`);
                }
                return response.text(); // Primeiro como texto para debug
            })
            .then(text => {
                console.log('� Resposta como texto:', text);

                try {
                    const data = JSON.parse(text);
                    console.log('📊 JSON parseado:', data);

                    if (data.success) {
                        foundOrderData = data.data;
                        messageSpan.textContent = '✅ ORDEM ENCONTRADA!';
                        messageSpan.className = 'text-success ms-2 fw-bold';
                        detailsBtn.style.display = 'inline-block';
                        detailsBtn.className = 'btn btn-success btn-sm';
                    } else {
                        foundOrderData = null;
                        messageSpan.textContent = '❌ ' + (data.message || 'Ordem não encontrada');
                        messageSpan.className = 'text-danger ms-2 fw-bold';
                        detailsBtn.style.display = 'none';
                    }
                } catch (parseError) {
                    console.error('❌ Erro ao fazer parse do JSON:', parseError);
                    messageSpan.textContent = '❌ Erro no formato da resposta';
                    messageSpan.className = 'text-danger ms-2 fw-bold';
                    detailsBtn.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('❌ ERRO COMPLETO:', error);
                messageSpan.textContent = '❌ ERRO: ' + error.message;
                messageSpan.className = 'text-danger ms-2 fw-bold';
                detailsBtn.style.display = 'none';
            })
            .finally(() => {
                // RESTAURAR BOTÃO
                searchBtn.innerHTML = '<i class="fas fa-search"></i> Buscar';
                searchBtn.disabled = false;
                searchBtn.className = 'btn btn-primary';
                searchBtn.style.animation = '';
                console.log('🔄 Busca finalizada');
            });
        }

        // Função para mostrar detalhes em JSON
        function showOrderDetails() {
            if (!foundOrderData) {
                alert('Nenhum dado encontrado');
                return;
            }

            // Fazer decode dos campos JSON que estão como string
            const decodedData = decodeJsonFields(foundOrderData);

            // Formatar JSON com indentação
            const formattedJson = JSON.stringify(decodedData, null, 2);

            // Criar modal para mostrar JSON
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content bg-dark text-white">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-file-code"></i> Detalhes da Ordem
                            </h5>
                            <div>
                                <button type="button" class="btn btn-primary btn-sm me-2" id="refazer-repasse-btn">
                                    <i class="fas fa-redo"></i> Refazer Repasse
                                </button>
                                <button type="button" class="btn btn-success btn-sm me-2" id="copy-json-btn">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                        </div>
                        <div class="modal-body p-3">
                            <pre id="json-content" style="
                                background-color: #1e1e1e;
                                color: #d4d4d4;
                                padding: 20px;
                                margin: 0;
                                border-radius: 8px;
                                font-family: 'Courier New', monospace;
                                font-size: 14px;
                                line-height: 1.5;
                                white-space: pre-wrap;
                                word-wrap: break-word;
                                border: none;
                                height: auto;
                                max-height: none;
                                overflow: visible;
                            ">${formattedJson}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Fechar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Adicionar funcionalidade de copy do JSON
            const copyBtn = modal.querySelector('#copy-json-btn');
            copyBtn.addEventListener('click', function() {
                // Copiar o JSON decodificado e formatado
                const jsonContent = JSON.stringify(decodedData, null, 2);

                // Usar a API moderna de clipboard se disponível
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(jsonContent).then(() => {
                        // Feedback visual de sucesso
                        const originalHtml = copyBtn.innerHTML;
                        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copiado!';
                        copyBtn.className = 'btn btn-success btn-sm me-2';

                        setTimeout(() => {
                            copyBtn.innerHTML = originalHtml;
                            copyBtn.className = 'btn btn-success btn-sm me-2';
                        }, 2000);
                    }).catch(err => {
                        console.error('Erro ao copiar:', err);
                        fallbackCopy(jsonContent, copyBtn);
                    });
                } else {
                    // Fallback para navegadores mais antigos
                    fallbackCopy(jsonContent, copyBtn);
                }
            });

            // Adicionar funcionalidade do botão "Refazer Repasse"
            const refazerBtn = modal.querySelector('#refazer-repasse-btn');
            refazerBtn.addEventListener('click', function() {
                // Bloquear funcionalidade por enquanto
                alert('⚠️ Funcionalidade em desenvolvimento!\n\nEsta função irá:\n1. Remover a ordem da base de dados\n2. Criar entrada na tabela order_status_timeline\n\nPor favor, aguarde a implementação completa.');

                // TODO: Implementar quando liberado
                // const orderId = decodedData.id;
                // refazerRepasseOrder(orderId);
            });

            // Remover modal após fechar
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }



        // Função para decodificar campos JSON que estão como string
        function decodeJsonFields(data) {
            const decoded = { ...data };

            // Lista de campos que podem conter JSON como string
            const jsonFields = [
                'customer_data',
                'billing_address',
                'shipping_address',
                'product_data',
                'payment_data',
                'order_data',
                'metadata',
                'extra_data',
                'custom_fields'
            ];

            // Percorrer todos os campos do objeto
            Object.keys(decoded).forEach(key => {
                const value = decoded[key];

                // Se o valor é uma string, tentar fazer parse JSON
                if (typeof value === 'string' && value.trim().length > 0) {
                    // Verificar se parece com JSON (começa com { ou [)
                    if ((value.trim().startsWith('{') && value.trim().endsWith('}')) ||
                        (value.trim().startsWith('[') && value.trim().endsWith(']'))) {
                        try {
                            decoded[key] = JSON.parse(value);
                            console.log(`✅ Campo '${key}' decodificado com sucesso`);
                        } catch (e) {
                            console.log(`⚠️ Campo '${key}' parece JSON mas não conseguiu decodificar:`, e.message);
                            // Manter o valor original se não conseguir fazer parse
                        }
                    }
                    // Verificar se é um JSON com escape (contém \")
                    else if (value.includes('\\"') || value.includes('\\{')) {
                        try {
                            // Tentar decodificar string com escape
                            const unescaped = value.replace(/\\"/g, '"').replace(/\\{/g, '{').replace(/\\}/g, '}');
                            if ((unescaped.trim().startsWith('{') && unescaped.trim().endsWith('}')) ||
                                (unescaped.trim().startsWith('[') && unescaped.trim().endsWith(']'))) {
                                decoded[key] = JSON.parse(unescaped);
                                console.log(`✅ Campo '${key}' decodificado com unescape`);
                            }
                        } catch (e) {
                            console.log(`⚠️ Campo '${key}' com escape não conseguiu decodificar:`, e.message);
                        }
                    }
                }
                // Se o valor já é um objeto, verificar se tem campos aninhados para decodificar
                else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    decoded[key] = decodeJsonFields(value);
                }
            });

            return decoded;
        }

        // Função fallback para copiar texto
        function fallbackCopy(text, button) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                // Feedback visual de sucesso
                const originalHtml = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copiado!';
                button.className = 'btn btn-success btn-sm me-2';

                setTimeout(() => {
                    button.innerHTML = originalHtml;
                    button.className = 'btn btn-success btn-sm me-2';
                }, 2000);
            } catch (err) {
                console.error('Erro ao copiar (fallback):', err);
                alert('Erro ao copiar. Selecione o texto manualmente.');
            }

            document.body.removeChild(textArea);
        }

        // Função para refazer repasse da ordem (BLOQUEADA)
        function refazerRepasseOrder(orderId) {
            console.log('🔄 Iniciando refazer repasse para ordem:', orderId);

            // Confirmar ação
            if (!confirm(`Tem certeza que deseja refazer o repasse da ordem ${orderId}?\n\nEsta ação irá:\n1. Remover a ordem da base de dados\n2. Criar nova entrada na timeline`)) {
                return;
            }

            // Dados para a nova entrada na timeline
            const timelineData = {
                order_id: orderId,
                status: 'REFAZER_REPASSE',
                channel: foundOrderData?.channel || 'N/A',
                country: foundOrderData?.country || 'N/A',
                data: new Date().toISOString().split('T')[0], // YYYY-MM-DD
                hora: new Date().toTimeString().split(' ')[0], // HH:MM:SS
                created_at: new Date().toISOString(),
                owner: 1
            };

            console.log('📊 Dados para timeline:', timelineData);

            // TODO: Implementar quando liberado
            /*
            fetch('/dashboard/refazer-repasse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    order_id: orderId,
                    timeline_data: timelineData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Repasse refeito com sucesso!');
                    // Recarregar dados
                    loadNovasOrdens();
                } else {
                    alert('❌ Erro ao refazer repasse: ' + (data.message || 'Erro desconhecido'));
                }
            })
            .catch(error => {
                console.error('❌ Erro na requisição:', error);
                alert('❌ Erro na comunicação com o servidor');
            });
            */
        }

        // Variável para armazenar dados do timeline encontrado
        let foundTimelineData = null;

        // Função para buscar timeline
        function searchTimeline() {
            const orderNumber = document.getElementById('timeline-search-input').value.trim();
            const searchBtn = document.getElementById('search-timeline-btn');
            const resultDiv = document.getElementById('timeline-search-result');
            const messageSpan = document.getElementById('timeline-search-message');
            const detailsBtn = document.getElementById('show-timeline-details-btn');

            console.log('🔍 Iniciando busca de timeline para:', orderNumber);

            if (!orderNumber) {
                messageSpan.textContent = 'Digite um Order ID';
                messageSpan.style.color = '#dc3545';
                resultDiv.style.display = 'block';
                detailsBtn.style.display = 'none';
                return;
            }

            // Mostrar loading
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando...';
            searchBtn.disabled = true;
            searchBtn.className = 'btn btn-warning';

            // Fazer requisição
            fetch('{{ route("dashboard.search-timeline") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    order_number: orderNumber
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('📊 Resposta da busca timeline:', data);

                if (data.success) {
                    foundTimelineData = data.data;
                    messageSpan.textContent = `Timeline encontrado! ${data.data.length} registros`;
                    messageSpan.style.color = '#28a745';
                    detailsBtn.style.display = 'inline-block';
                } else {
                    foundTimelineData = null;
                    messageSpan.textContent = data.message || 'Timeline não encontrado';
                    messageSpan.style.color = '#dc3545';
                    detailsBtn.style.display = 'none';
                }

                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error('❌ Erro na busca timeline:', error);
                foundTimelineData = null;
                messageSpan.textContent = 'Erro na busca';
                messageSpan.style.color = '#dc3545';
                resultDiv.style.display = 'block';
                detailsBtn.style.display = 'none';
            })
            .finally(() => {
                // Restaurar botão
                searchBtn.innerHTML = '<i class="fas fa-search"></i> Buscar';
                searchBtn.disabled = false;
                searchBtn.className = 'btn btn-info';
            });
        }

        // Função para calcular diferença de tempo entre registros
        function calculateTimeDifference(date1, date2) {
            const d1 = new Date(date1);
            const d2 = new Date(date2);
            const diffMs = Math.abs(d2 - d1);

            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays > 0) {
                const remainingHours = diffHours % 24;
                return `${diffDays}d ${remainingHours}h`;
            } else if (diffHours > 0) {
                const remainingMinutes = diffMinutes % 60;
                return `${diffHours}h ${remainingMinutes}m`;
            } else {
                return `${diffMinutes}m`;
            }
        }

        // Função para gerar timeline visual
        function generateTimelineVisual(timelineData) {
            // Ordenar em ordem descendente (mais recente primeiro)
            const sortedData = [...timelineData].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            let timelineHtml = '<div class="timeline-visual">';

            sortedData.forEach((item, index) => {
                const isLast = index === sortedData.length - 1;
                // Para ordem descendente, calcular diferença com o próximo item (anterior cronologicamente)
                const timeDiff = index < sortedData.length - 1 ? calculateTimeDifference(item.created_at, sortedData[index + 1].created_at) : null;

                // Cores baseadas no status
                const statusColors = {
                    'WAITING_ACCEPTANCE': '#ffc107',
                    'WAITING_DEBIT_PAYMENT': '#17a2b8',
                    'STAGING': '#007bff',
                    'CREATED': '#28a745',
                    'SHIPPING': '#fd7e14',
                    'SUPPORT_TRANSFER': '#e83e8c',
                    'SELLER_TRANSFER': '#6f42c1'
                };

                const color = statusColors[item.status] || '#6c757d';

                timelineHtml += `
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background-color: ${color};">
                            <i class="fas fa-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h6 class="timeline-status" style="color: ${color};">${item.status}</h6>
                            </div>
                            <div class="timeline-details">
                                <div class="timeline-time">${formatDateTime(item.data, item.hora)}</div>
                                ${timeDiff ? `<div class="timeline-duration"><span class="badge">+${timeDiff}</span></div>` : ''}
                            </div>
                        </div>
                        ${!isLast ? '<div class="timeline-line"></div>' : ''}
                    </div>
                `;
            });

            timelineHtml += '</div>';
            return timelineHtml;
        }

        // Função para mostrar detalhes do timeline
        function showTimelineDetails() {
            if (!foundTimelineData) {
                alert('Nenhum timeline encontrado');
                return;
            }

            // Formatar JSON com indentação
            const formattedJson = JSON.stringify(foundTimelineData, null, 2);

            // Gerar timeline visual
            const timelineVisual = generateTimelineVisual(foundTimelineData);

            // Criar modal para mostrar JSON e Timeline
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog" style="max-width: 75vw; width: 75vw;">
                    <div class="modal-content bg-dark text-white" style="height: 80vh; max-height: 80vh;">
                        <div class="modal-header flex-shrink-0">
                            <h5 class="modal-title">
                                <i class="fas fa-clock"></i> Timeline da Ordem ${foundTimelineData[0]?.order_id || ''}
                            </h5>
                            <div>
                                <button type="button" class="btn btn-success btn-sm me-2" id="copy-timeline-btn">
                                    <i class="fas fa-copy"></i> Copy JSON
                                </button>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                        </div>
                        <div class="modal-body p-0 flex-grow-1 overflow-hidden">
                            <div class="row g-0 h-100">
                                <!-- JSON à esquerda -->
                                <div class="col-md-6 border-end border-secondary d-flex flex-column">
                                    <div class="p-3 flex-shrink-0">
                                        <h6 class="mb-0"><i class="fas fa-code"></i> JSON Data</h6>
                                    </div>
                                    <div class="flex-grow-1 overflow-auto px-3 pb-3">
                                        <pre id="timeline-content" style="
                                            background-color: #1e1e1e;
                                            color: #d4d4d4;
                                            padding: 15px;
                                            margin: 0;
                                            border-radius: 8px;
                                            font-family: 'Courier New', monospace;
                                            font-size: 11px;
                                            line-height: 1.3;
                                            white-space: pre-wrap;
                                            word-wrap: break-word;
                                            border: none;
                                            min-height: 100%;
                                        ">${formattedJson}</pre>
                                    </div>
                                </div>
                                <!-- Timeline visual à direita -->
                                <div class="col-md-6 d-flex flex-column">
                                    <div class="p-3 flex-shrink-0">
                                        <h6 class="mb-2"><i class="fas fa-timeline"></i> Timeline Visual</h6>
                                        <div class="order-number-display mb-3" style="
                                            background: linear-gradient(135deg, #007bff, #0056b3);
                                            color: white;
                                            padding: 8px 12px;
                                            border-radius: 6px;
                                            text-align: center;
                                            font-weight: bold;
                                            font-size: 14px;
                                            border: 1px solid rgba(255,255,255,0.2);
                                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                        ">
                                            ${foundTimelineData[0]?.order_id || 'N/A'} - ${foundTimelineData[0]?.channel || 'N/A'}/${foundTimelineData[0]?.country || 'N/A'}
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 overflow-auto px-3 pb-3">
                                        ${timelineVisual}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer flex-shrink-0">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Fechar
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Adicionar estilos CSS para a timeline
            const timelineStyles = document.createElement('style');
            timelineStyles.textContent = `
                .timeline-visual {
                    position: relative;
                    padding: 10px 0;
                    min-height: 100%;
                }

                .timeline-item {
                    position: relative;
                    padding-left: 35px;
                    margin-bottom: 25px;
                }

                .timeline-item:last-child {
                    margin-bottom: 0;
                }

                .timeline-marker {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 18px;
                    height: 18px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 2px solid #343a40;
                    z-index: 2;
                }

                .timeline-marker i {
                    font-size: 7px;
                    color: white;
                }

                .timeline-line {
                    position: absolute;
                    left: 8px;
                    top: 18px;
                    width: 2px;
                    height: 35px;
                    background-color: #495057;
                    z-index: 1;
                }

                .timeline-content {
                    background-color: #2d3748;
                    border-radius: 6px;
                    padding: 12px;
                    border-left: 3px solid #495057;
                    margin-left: 8px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }

                .timeline-header {
                    margin-bottom: 8px;
                }

                .timeline-status {
                    margin: 0;
                    font-weight: bold;
                    font-size: 13px;
                    word-wrap: break-word;
                }

                .timeline-details {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .timeline-time {
                    color: #adb5bd;
                    font-size: 11px;
                    font-weight: 500;
                }

                .timeline-duration {
                    margin-top: 2px;
                }

                .timeline-duration .badge {
                    font-size: 13px;
                    padding: 2px 5px;
                    font-weight: 400;
                    background-color: #2c3034 !important;
                }

                .timeline-item:hover .timeline-content {
                    background-color: #374151;
                    transform: translateX(1px);
                    transition: all 0.2s ease;
                }

                /* Responsividade para telas menores */
                @media (max-width: 768px) {
                    .timeline-item {
                        padding-left: 30px;
                        margin-bottom: 20px;
                    }

                    .timeline-marker {
                        width: 16px;
                        height: 16px;
                    }

                    .timeline-line {
                        left: 7px;
                        height: 30px;
                    }

                    .timeline-status {
                        font-size: 12px;
                    }

                    .timeline-time {
                        font-size: 10px;
                    }
                }
            `;
            modal.appendChild(timelineStyles);

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Adicionar funcionalidade de copy do JSON
            const copyBtn = modal.querySelector('#copy-timeline-btn');
            copyBtn.addEventListener('click', function() {
                // Copiar o JSON do timeline
                const jsonContent = JSON.stringify(foundTimelineData, null, 2);

                // Usar a API moderna de clipboard se disponível
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(jsonContent).then(() => {
                        // Feedback visual de sucesso
                        const originalHtml = copyBtn.innerHTML;
                        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copiado!';
                        copyBtn.className = 'btn btn-success btn-sm me-2';

                        setTimeout(() => {
                            copyBtn.innerHTML = originalHtml;
                            copyBtn.className = 'btn btn-success btn-sm me-2';
                        }, 2000);
                    }).catch(err => {
                        console.error('Erro ao copiar:', err);
                        fallbackCopy(jsonContent, copyBtn);
                    });
                } else {
                    // Fallback para navegadores mais antigos
                    fallbackCopy(jsonContent, copyBtn);
                }
            });

            // Remover modal após fechar
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Inicialização do dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Dashboard iniciado');
            console.log('🔧 Iniciando countdown timer...');

            // Event listeners para busca de ordem (com verificação)
            const searchBtn = document.getElementById('search-order-btn');
            const detailsBtn = document.getElementById('show-details-btn');
            const searchInput = document.getElementById('order-search-input');

            if (searchBtn) {
                searchBtn.addEventListener('click', searchOrder);
            }

            if (detailsBtn) {
                detailsBtn.addEventListener('click', showOrderDetails);
            }

            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchOrder();
                    }
                });
            }

            // Event listeners para busca de timeline
            const timelineSearchBtn = document.getElementById('search-timeline-btn');
            const timelineDetailsBtn = document.getElementById('show-timeline-details-btn');
            const timelineSearchInput = document.getElementById('timeline-search-input');

            if (timelineSearchBtn) {
                timelineSearchBtn.addEventListener('click', searchTimeline);
            }

            if (timelineDetailsBtn) {
                timelineDetailsBtn.addEventListener('click', showTimelineDetails);
            }

            if (timelineSearchInput) {
                timelineSearchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchTimeline();
                    }
                });
            }

            // Event listeners para botões de filtro da tabela 33
            const filterButtons = document.querySelectorAll('#status-filter-buttons-33 button');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const status = this.getAttribute('data-status');
                    console.log('🔘 Filtro clicado:', status);
                    filterTable33ByStatus(status);
                });
            });

            // Aguardar um pouco para garantir que o DOM está pronto
            setTimeout(() => {
                loadNovasOrdens(); // Carrega ordens e marketplaces juntos
                startCountdown();
                console.log('✅ Timer iniciado com sucesso');
            }, 100);

        });

        // Função para mostrar modal com detalhes da order
        function showOrderDetailsModal(orderId) {
            console.log('🔍 Buscando detalhes da order:', orderId);

            // Mostrar loading no modal
            const modalBody = document.getElementById('order-details-modal-body');
            modalBody.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando detalhes da order...</p>
                </div>
            `;

            // Atualizar título do modal
            document.getElementById('order-details-modal-title').textContent = `Detalhes da Order: ${orderId}`;

            // Mostrar modal
            const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
            modal.show();

            // Buscar dados da API
            fetch(`/dashboard/order-details/${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayOrderDetails(data.data);
                    } else {
                        modalBody.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                Erro ao carregar detalhes: ${data.message}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Erro ao buscar detalhes da order:', error);
                    modalBody.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Erro de conexão ao buscar detalhes da order.
                        </div>
                    `;
                });
        }

        // Função para exibir os detalhes da order no modal
        function displayOrderDetails(orderData) {
            const modalBody = document.getElementById('order-details-modal-body');

            // Formatar JSON de forma legível
            const formattedJson = JSON.stringify(orderData, null, 2);

            modalBody.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">JSON Response:</h6>
                    <button class="btn btn-outline-secondary btn-sm" onclick="copyOrderDetailsToClipboard()">
                        <i class="fas fa-copy"></i> Copiar
                    </button>
                </div>
                <pre id="order-details-json" style="background-color: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; max-height: 500px; overflow-y: auto; font-size: 0.9rem;"><code>${escapeHtml(formattedJson)}</code></pre>
            `;
        }

        // Função para copiar JSON para clipboard
        function copyOrderDetailsToClipboard() {
            const jsonElement = document.getElementById('order-details-json');
            const text = jsonElement.textContent;

            navigator.clipboard.writeText(text).then(() => {
                // Feedback visual
                const button = event.target.closest('button');
                const originalHtml = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copiado!';
                button.classList.remove('btn-outline-secondary');
                button.classList.add('btn-success');

                setTimeout(() => {
                    button.innerHTML = originalHtml;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
            }).catch(err => {
                console.error('Erro ao copiar:', err);
                alert('Erro ao copiar para clipboard');
            });
        }

        // Função para escapar HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Função removida - busca não funcional
    </script>

    <!-- Modal para Detalhes da Order -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content bg-dark text-light">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="order-details-modal-title">Detalhes da Order</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="order-details-modal-body">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
